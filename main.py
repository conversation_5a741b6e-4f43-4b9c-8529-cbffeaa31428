#!/usr/bin/env python3
"""
主启动文件 - 使用 fastapi_mcp 自动暴露 MCP 工具
"""
import uvicorn
import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi_mcp import FastApiMCP

from app.core.config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    app = FastAPI(
        title=settings.PROJECT_NAME,
        description=settings.PROJECT_DESCRIPTION,
        version=settings.VERSION,
        openapi_url=f"{settings.API_V1_STR}/openapi.json",
    )

    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 包含业务域路由
    try:
        from app.mcpController.domains.certificate_controller import certificate_router
        app.include_router(certificate_router, prefix=f"{settings.API_V1_STR}")
        logger.info("证书域路由加载成功")
    except Exception as e:
        logger.error(f"证书域路由加载失败: {str(e)}")

    try:
        from app.mcpController.domains.platform_controller import platform_router
        app.include_router(platform_router, prefix=f"{settings.API_V1_STR}")
        logger.info("平台功能路由加载成功")
    except Exception as e:
        logger.error(f"平台功能路由加载失败: {str(e)}")

    # 其他路由暂时注释，逐步添加
    # try:
    #     from app.mcpController.domains.signing_controller import signing_router
    #     from app.mcpController.domains.saas_controller import saas_router
    #     from app.mcpController.domains.identity_controller import identity_router
    #     from app.mcpController.domains.intention_controller import intention_router
    #
    #     app.include_router(signing_router, prefix=f"{settings.API_V1_STR}")
    #     app.include_router(saas_router, prefix=f"{settings.API_V1_STR}")
    #     app.include_router(identity_router, prefix=f"{settings.API_V1_STR}")
    #     app.include_router(intention_router, prefix=f"{settings.API_V1_STR}")
    # except Exception as e:
    #     logger.error(f"其他业务域路由加载失败: {str(e)}")

    # 添加健康检查端点
    @app.get("/health_check")
    async def health_check():
        return {"status": "healthy", "service": settings.PROJECT_NAME}

    # 设置 MCP 服务 - 使用 fastapi_mcp 自动暴露工具
    setup_mcp_service(app)

    return app


def setup_mcp_service(app: FastAPI):
    """设置MCP服务 - 使用 fastapi_mcp 自动暴露工具"""
    try:
        logger.info("🚀 设置MCP服务...")

        # 使用 fastapi_mcp 自动暴露已加载的业务域工具
        mcp = FastApiMCP(
            app,
            include_tags=["证书域", "平台功能"]  # 只暴露已加载的业务域
        )

        # 挂载 MCP 服务
        mcp.mount()

        logger.info("✅ MCP服务设置完成 - 自动暴露所有业务工具")
        return True

    except Exception as e:
        logger.error(f"❌ MCP服务设置失败: {str(e)}")
        logger.warning("⚠️ 跳过MCP服务设置，继续启动基本服务")
        return None


def main():
    """主函数"""
    try:
        logger.info(f"🚀 启动 {settings.PROJECT_NAME}...")

        # 创建FastAPI应用
        app = create_app()

        # 启动服务器
        logger.info(f"🌐 服务将在 http://localhost:{settings.PORT} 启动")
        logger.info(f"📚 API文档: http://localhost:{settings.PORT}/docs")
        logger.info(f"🔌 MCP端点: http://localhost:{settings.PORT}/mcp")

        uvicorn.run(
            app,
            host=settings.HOST,
            port=settings.PORT,
            log_level="info"
        )

    except KeyboardInterrupt:
        logger.info("👋 服务已停止")
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {str(e)}")
        raise


if __name__ == "__main__":
    main()
