# 测试环境配置文件
environment: test
description: "测试环境配置 - 连接真实的测试服务器"

# API服务地址配置
api_urls:
  sdk: "http://sdk.testk8s.tsign.cn"
  cert: "http://cert-service.testk8s.tsign.cn"
  footstone: "http://in-test-openapi.tsign.cn"

# 应用ID配置
app_ids:
  default: "7876611670"
  cert: "1111564052"
  signing: "7876722740"

# 默认请求头配置
headers:
  Content-Type: "application/json"
  X-Tsign-Open-Auth-Mode: "simple"
  X-Tsign-Service-Group: "DEFAULT"

# 业务域配置
business_domains:
  signing:
    name: "签署域"
    description: "电子签名相关功能"
    app_id: "7876722740"
    base_url: "http://sdk.testk8s.tsign.cn"
    
  saas:
    name: "SaaS域"
    description: "SaaS平台相关功能"
    app_id: "7876611670"
    base_url: "http://sdk.testk8s.tsign.cn"
    
  identity:
    name: "实名域"
    description: "实名认证相关功能"
    app_id: "7876611670"
    base_url: "http://in-test-openapi.tsign.cn"
    
  intention:
    name: "意愿域"
    description: "签署意愿相关功能"
    app_id: "7876611670"
    base_url: "http://sdk.testk8s.tsign.cn"
    
  certificate:
    name: "证书域"
    description: "数字证书相关功能"
    app_id: "1111564052"
    base_url: "http://cert-service.testk8s.tsign.cn"

# 请求配置
request_config:
  timeout: 30
  retry_times: 3
  log_requests: true
