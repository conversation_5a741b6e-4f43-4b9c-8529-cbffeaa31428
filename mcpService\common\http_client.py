#!/usr/bin/env python3
"""
HTTP客户端 - 统一HTTP请求处理
支持环境切换、请求日志、错误处理
"""
import json
import requests
import logging
from typing import Dict, Any, Optional
from .environment_manager import env_manager
from app.core.config import settings

logger = logging.getLogger(__name__)


class HttpClient:
    """HTTP客户端"""
    
    def __init__(self):
        self.timeout = settings.REQUEST_TIMEOUT
        self.log_requests = settings.LOG_REQUESTS
    
    def make_request(
        self, 
        url: str, 
        method: str = "POST", 
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        environment: Optional[str] = None,
        domain: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        发送HTTP请求
        
        Args:
            url: 请求地址
            method: 请求方法，默认POST
            data: 请求数据
            headers: 请求头
            params: URL参数
            environment: 环境描述
            domain: 业务域
            
        Returns:
            响应结果
        """
        try:
            # 处理环境
            if environment:
                env_manager.detect_environment(environment)
            
            # 获取默认请求头
            default_headers = env_manager.get_headers()
            
            # 合并请求头
            if headers:
                merged_headers = {**default_headers, **headers}
            else:
                merged_headers = default_headers
            
            # 如果指定了业务域，使用业务域的应用ID
            if domain:
                domain_config = env_manager.get_domain_config(domain)
                if domain_config and "app_id" in domain_config:
                    merged_headers["X-Tsign-Open-App-Id"] = domain_config["app_id"]
            
            # 记录请求信息
            if self.log_requests:
                logger.info(f"HTTP请求: {method} {url}")
                logger.info(f"请求头: {json.dumps(merged_headers, ensure_ascii=False)}")
                if data:
                    logger.info(f"请求数据: {json.dumps(data, ensure_ascii=False)}")
            
            # 发送请求
            if method.upper() == "GET":
                response = requests.get(
                    url, 
                    headers=merged_headers,
                    params=params or data,
                    timeout=self.timeout
                )
            elif method.upper() == "POST":
                response = requests.post(
                    url, 
                    headers=merged_headers,
                    json=data,
                    params=params,
                    timeout=self.timeout
                )
            elif method.upper() == "PUT":
                response = requests.put(
                    url, 
                    headers=merged_headers,
                    json=data,
                    params=params,
                    timeout=self.timeout
                )
            elif method.upper() == "DELETE":
                response = requests.delete(
                    url, 
                    headers=merged_headers,
                    json=data,
                    params=params,
                    timeout=self.timeout
                )
            else:
                return {
                    "status": "error",
                    "message": f"不支持的请求方法: {method}"
                }
            
            # 处理响应
            try:
                result = response.json()
            except ValueError:
                result = {"text": response.text}
            
            # 记录响应信息
            if self.log_requests:
                logger.info(f"HTTP响应: {response.status_code}")
                logger.info(f"响应数据: {json.dumps(result, ensure_ascii=False)}")
            
            # 添加请求和响应信息
            result["_request"] = {
                "url": url,
                "method": method,
                "headers": merged_headers,
                "data": data,
                "params": params
            }
            
            result["_response"] = {
                "status_code": response.status_code,
                "headers": dict(response.headers)
            }
            
            return result
            
        except requests.RequestException as e:
            logger.error(f"HTTP请求异常: {str(e)}")
            return {
                "status": "error",
                "message": f"HTTP请求异常: {str(e)}",
                "_request": {
                    "url": url,
                    "method": method,
                    "headers": merged_headers if 'merged_headers' in locals() else {},
                    "data": data,
                    "params": params
                }
            }
        except Exception as e:
            logger.error(f"请求处理异常: {str(e)}")
            return {
                "status": "error",
                "message": f"请求处理异常: {str(e)}"
            }


# 全局HTTP客户端实例
http_client = HttpClient()
