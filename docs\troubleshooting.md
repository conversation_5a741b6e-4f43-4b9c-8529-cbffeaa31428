# 故障排除指南

## 📋 目录

- [常见问题](#常见问题)
- [环境问题](#环境问题)
- [API问题](#api问题)
- [MCP问题](#mcp问题)
- [性能问题](#性能问题)
- [日志分析](#日志分析)
- [联系支持](#联系支持)

## 🔍 常见问题

### 服务启动失败

**症状**: 服务无法启动，或启动后立即崩溃

**可能原因**:
1. 端口被占用
2. 依赖包缺失
3. 配置文件错误
4. 权限问题

**解决方案**:

1. **检查端口占用**:
   ```bash
   # 检查端口是否被占用
   netstat -tlnp | grep 8000
   
   # 如果被占用，可以终止占用进程
   kill -9 <PID>
   ```

2. **检查依赖**:
   ```bash
   # 重新安装依赖
   pip install -r requirements.txt
   
   # 检查依赖版本
   pip list | grep fastapi
   ```

3. **检查配置**:
   ```bash
   # 确保配置文件存在
   ls -la config/
   
   # 验证配置文件格式
   python -c "import yaml; yaml.safe_load(open('config/test_env.yml'))"
   ```

4. **检查日志**:
   ```bash
   # 查看启动日志
   tail -f logs/app.log
   ```

### 环境切换失败

**症状**: 环境切换不生效，或返回错误

**可能原因**:
1. 环境配置文件不存在
2. 环境配置格式错误
3. 环境检测逻辑问题

**解决方案**:

1. **检查配置文件**:
   ```bash
   # 确认配置文件存在
   ls -la config/test_env.yml config/mock_env.yml
   
   # 检查配置文件权限
   chmod 644 config/*.yml
   ```

2. **手动切换环境**:
   ```bash
   # 使用API切换环境
   curl -X POST http://localhost:8000/api/v1/platform/switch_environment \
     -H "Content-Type: application/json" \
     -d '{"environment": "test"}'
   ```

3. **检查环境检测代码**:
   ```python
   # 在代码中添加调试日志
   logger.debug(f"环境检测输入: {text}")
   logger.debug(f"检测结果: {detected_env}")
   ```

### 自动代码生成失败

**症状**: 自动代码生成功能不工作或生成错误代码

**可能原因**:
1. 触发关键词不正确
2. curl命令格式错误
3. 解析逻辑问题

**解决方案**:

1. **确认触发关键词**:
   ```
   # 确保包含正确的触发关键词
   接入mcp
   ```

2. **检查curl命令格式**:
   ```bash
   # 使用简化的curl命令
   curl --location --request POST 'http://example.com/api' \
     --header 'Content-Type: application/json' \
     --data-raw '{"key": "value"}'
   ```

3. **手动测试代码生成**:
   ```bash
   curl -X POST http://localhost:8000/api/v1/platform/generate_mcp_code \
     -H "Content-Type: application/json" \
     -d '{
       "curl_command": "curl --location --request POST \"http://example.com/api\" --header \"Content-Type: application/json\" --data-raw \"{\\\"key\\\": \\\"value\\\"}\"",
       "domain": "test"
     }'
   ```

## 🌐 环境问题

### 环境配置问题

**症状**: 环境配置加载失败或配置不正确

**解决方案**:

1. **检查配置文件格式**:
   ```bash
   # 验证YAML格式
   python -c "import yaml; print(yaml.safe_load(open('config/test_env.yml')))"
   ```

2. **创建默认配置**:
   ```bash
   # 复制示例配置
   cp docs/examples/test_env.yml.example config/test_env.yml
   cp docs/examples/mock_env.yml.example config/mock_env.yml
   ```

3. **检查配置目录权限**:
   ```bash
   # 确保配置目录可写
   chmod -R 755 config/
   ```

### 环境变量问题

**症状**: 环境变量未生效或值不正确

**解决方案**:

1. **检查环境变量**:
   ```bash
   # 打印环境变量
   env | grep PORT
   ```

2. **设置环境变量**:
   ```bash
   # 临时设置
   export PORT=8000
   
   # 永久设置（添加到.env文件）
   echo "PORT=8000" >> .env
   ```

3. **使用dotenv加载**:
   ```python
   # 在代码中添加
   from dotenv import load_dotenv
   load_dotenv()
   ```

## 🔌 API问题

### API调用失败

**症状**: API返回错误或超时

**可能原因**:
1. 服务未启动
2. 参数错误
3. 外部服务不可用
4. 网络问题

**解决方案**:

1. **检查服务状态**:
   ```bash
   # 检查服务是否运行
   curl http://localhost:8000/health_check
   ```

2. **检查参数**:
   ```bash
   # 使用正确的参数格式
   curl -X POST http://localhost:8000/api/v1/certificate/get_test_account \
     -H "Content-Type: application/json" \
     -d '{}'
   ```

3. **检查外部服务**:
   ```bash
   # 测试外部API
   curl http://sdk.testk8s.tsign.cn/random/get
   ```

4. **启用详细日志**:
   ```bash
   # 设置日志级别
   export LOG_LEVEL=DEBUG
   ```

### 响应格式错误

**症状**: API响应格式不符合预期

**解决方案**:

1. **检查响应格式化器**:
   ```python
   # 在代码中添加调试
   logger.debug(f"原始响应: {api_result}")
   logger.debug(f"格式化后: {formatter.format_api_response(api_result)}")
   ```

2. **手动格式化测试**:
   ```python
   from mcpService.common.response_formatter import formatter
   
   test_data = {"success": True, "data": {"key": "value"}}
   formatted = formatter.success(data=test_data)
   print(formatted)
   ```

## 🤖 MCP问题

### MCP工具不可见

**症状**: AI工具无法发现MCP工具

**可能原因**:
1. MCP服务未正确挂载
2. 工具标签配置错误
3. MCP端点不可访问

**解决方案**:

1. **检查MCP挂载**:
   ```python
   # 确保正确挂载MCP
   mcp = FastApiMCP(
       app,
       include_tags=["签署域", "SaaS域", "实名域", "意愿域", "证书域", "平台功能"]
   )
   mcp.mount()
   ```

2. **检查工具标签**:
   ```python
   # 确保控制器使用正确的标签
   certificate_router = APIRouter(
       prefix="/certificate",
       tags=["证书域"]  # 必须匹配include_tags
   )
   ```

3. **测试MCP端点**:
   ```bash
   # 检查MCP端点
   curl http://localhost:8000/mcp
   ```

### MCP工具调用失败

**症状**: MCP工具调用返回错误

**解决方案**:

1. **直接测试工具调用**:
   ```bash
   # 手动调用MCP工具
   curl -X POST http://localhost:8000/mcp/tools/call \
     -H "Content-Type: application/json" \
     -d '{
       "name": "certificate_get_test_account",
       "arguments": {}
     }'
   ```

2. **检查工具参数**:
   ```bash
   # 使用正确的参数格式
   curl -X POST http://localhost:8000/mcp/tools/call \
     -H "Content-Type: application/json" \
     -d '{
       "name": "certificate_create",
       "arguments": {
         "cert_name": "测试用户",
         "phone": "***********",
         "idcard": "123456789012345678"
       }
     }'
   ```

3. **检查工具注册**:
   ```bash
   # 获取所有工具列表
   curl http://localhost:8000/mcp/tools/list
   ```

## ⚡ 性能问题

### 响应缓慢

**症状**: API响应时间长，系统负载高

**可能原因**:
1. 系统资源不足
2. 外部API响应慢
3. 代码效率问题
4. 连接池配置不当

**解决方案**:

1. **检查系统资源**:
   ```bash
   # 检查CPU和内存使用
   top
   
   # 检查磁盘IO
   iostat -x 1
   ```

2. **检查外部API**:
   ```bash
   # 测试外部API响应时间
   time curl http://sdk.testk8s.tsign.cn/random/get
   ```

3. **启用性能监控**:
   ```bash
   # 获取性能指标
   curl http://localhost:8000/api/v1/platform/get_performance_metrics
   ```

4. **优化连接池**:
   ```python
   # 调整HTTP客户端配置
   http_client = httpx.AsyncClient(
       limits=httpx.Limits(
           max_keepalive_connections=20,
           max_connections=100
       ),
       timeout=httpx.Timeout(30.0)
   )
   ```

### 内存泄漏

**症状**: 服务运行一段时间后内存占用持续增长

**解决方案**:

1. **监控内存使用**:
   ```bash
   # 查看进程内存使用
   ps -o pid,rss,command -p $(pgrep -f "python main.py")
   ```

2. **使用内存分析工具**:
   ```python
   # 添加内存分析
   import tracemalloc
   
   tracemalloc.start()
   # ... 代码执行 ...
   snapshot = tracemalloc.take_snapshot()
   top_stats = snapshot.statistics('lineno')
   print("[ Top 10 ]")
   for stat in top_stats[:10]:
       print(stat)
   ```

3. **定期重启服务**:
   ```bash
   # 创建定时任务重启服务
   crontab -e
   # 添加: 0 4 * * * systemctl restart esign-mcp
   ```

## 📊 日志分析

### 日志级别调整

```python
# 设置详细日志级别
import logging
logging.basicConfig(level=logging.DEBUG)

# 或者通过环境变量
import os
os.environ["LOG_LEVEL"] = "DEBUG"
```

### 常见日志错误

1. **连接错误**:
   ```
   ERROR - HTTP请求异常: Connection refused
   ```
   解决: 检查外部服务地址和网络连接

2. **超时错误**:
   ```
   ERROR - HTTP请求异常: Read timed out
   ```
   解决: 增加超时时间或检查外部服务性能

3. **认证错误**:
   ```
   ERROR - API调用失败: 401 Unauthorized
   ```
   解决: 检查应用ID和认证信息

4. **参数错误**:
   ```
   ERROR - API调用失败: 400 Bad Request
   ```
   解决: 检查请求参数格式和值

### 日志搜索技巧

```bash
# 搜索错误日志
grep "ERROR" logs/app.log

# 搜索特定API
grep "certificate_create" logs/app.log

# 搜索特定时间段
grep "2025-01-10" logs/app.log

# 统计错误次数
grep -c "ERROR" logs/app.log

# 查看最近的错误
tail -n 100 logs/app.log | grep "ERROR"
```

## 🔧 故障排除工具

### 健康检查工具

```bash
#!/bin/bash
# health_check.sh

echo "=== 系统健康检查 ==="

# 检查服务状态
echo -n "服务状态: "
if curl -s http://localhost:8000/health_check > /dev/null; then
    echo "正常"
else
    echo "异常"
fi

# 检查API响应
echo -n "API响应: "
RESPONSE=$(curl -s -X POST http://localhost:8000/api/v1/platform/get_version_info)
if [[ $RESPONSE == *"success"* ]]; then
    echo "正常"
else
    echo "异常"
fi

# 检查MCP端点
echo -n "MCP端点: "
if curl -s http://localhost:8000/mcp > /dev/null; then
    echo "正常"
else
    echo "异常"
fi

# 检查系统资源
echo "系统资源:"
echo "- CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}')%"
echo "- 内存使用率: $(free -m | awk 'NR==2{printf "%.2f%%", $3*100/$2}')"
echo "- 磁盘使用率: $(df -h / | awk 'NR==2{print $5}')"

echo "=== 检查完成 ==="
```

### 性能测试工具

```python
# performance_test.py
import time
import requests
import statistics
from concurrent.futures import ThreadPoolExecutor

def test_api(endpoint, payload=None, iterations=100):
    """测试API性能"""
    url = f"http://localhost:8000{endpoint}"
    times = []
    
    for i in range(iterations):
        start = time.time()
        response = requests.post(url, json=payload or {})
        end = time.time()
        
        if response.status_code == 200:
            times.append(end - start)
        else:
            print(f"请求失败: {response.status_code} - {response.text}")
    
    if times:
        return {
            "min": min(times),
            "max": max(times),
            "avg": statistics.mean(times),
            "median": statistics.median(times),
            "p95": sorted(times)[int(len(times) * 0.95)],
            "success_rate": len(times) / iterations * 100
        }
    return None

def concurrent_test(endpoint, payload=None, users=10, requests_per_user=10):
    """并发测试"""
    url = f"http://localhost:8000{endpoint}"
    results = []
    
    def worker():
        for _ in range(requests_per_user):
            start = time.time()
            response = requests.post(url, json=payload or {})
            end = time.time()
            results.append({
                "time": end - start,
                "status": response.status_code
            })
    
    with ThreadPoolExecutor(max_workers=users) as executor:
        futures = [executor.submit(worker) for _ in range(users)]
        for future in futures:
            future.result()
    
    success = [r["time"] for r in results if r["status"] == 200]
    if success:
        return {
            "min": min(success),
            "max": max(success),
            "avg": statistics.mean(success),
            "median": statistics.median(success),
            "p95": sorted(success)[int(len(success) * 0.95)],
            "success_rate": len(success) / len(results) * 100,
            "total_requests": len(results)
        }
    return None

if __name__ == "__main__":
    print("=== API性能测试 ===")
    
    print("\n1. 测试账号API")
    result = test_api("/api/v1/certificate/get_test_account")
    print(f"最小响应时间: {result['min']:.3f}s")
    print(f"最大响应时间: {result['max']:.3f}s")
    print(f"平均响应时间: {result['avg']:.3f}s")
    print(f"中位响应时间: {result['median']:.3f}s")
    print(f"95%响应时间: {result['p95']:.3f}s")
    print(f"成功率: {result['success_rate']:.2f}%")
    
    print("\n2. 并发测试 (10用户，每用户10请求)")
    result = concurrent_test("/api/v1/certificate/get_test_account", users=10, requests_per_user=10)
    print(f"最小响应时间: {result['min']:.3f}s")
    print(f"最大响应时间: {result['max']:.3f}s")
    print(f"平均响应时间: {result['avg']:.3f}s")
    print(f"中位响应时间: {result['median']:.3f}s")
    print(f"95%响应时间: {result['p95']:.3f}s")
    print(f"成功率: {result['success_rate']:.2f}%")
    print(f"总请求数: {result['total_requests']}")
```

## 📞 联系支持

### 提交问题

提交问题时，请包含以下信息：

1. **问题描述**：详细描述问题症状
2. **环境信息**：操作系统、Python版本、依赖版本
3. **复现步骤**：如何复现问题
4. **日志信息**：相关错误日志
5. **已尝试的解决方案**：已经尝试过的方法

### 收集诊断信息

```bash
#!/bin/bash
# collect_diagnostics.sh

DIAG_DIR="diagnostics_$(date +%Y%m%d_%H%M%S)"
mkdir -p $DIAG_DIR

# 收集系统信息
echo "收集系统信息..."
uname -a > $DIAG_DIR/system_info.txt
python --version >> $DIAG_DIR/system_info.txt
pip list > $DIAG_DIR/pip_list.txt

# 收集配置文件
echo "收集配置文件..."
cp -r config/ $DIAG_DIR/

# 收集日志
echo "收集日志..."
cp -r logs/ $DIAG_DIR/

# 收集健康状态
echo "收集健康状态..."
curl -s http://localhost:8000/health_check > $DIAG_DIR/health_check.json
curl -s http://localhost:8000/api/v1/platform/get_system_status > $DIAG_DIR/system_status.json
curl -s http://localhost:8000/api/v1/platform/get_api_statistics > $DIAG_DIR/api_statistics.json

# 打包诊断信息
tar -czf ${DIAG_DIR}.tar.gz $DIAG_DIR
rm -rf $DIAG_DIR

echo "诊断信息已收集到: ${DIAG_DIR}.tar.gz"
```

### 紧急联系方式

- **技术支持**: <EMAIL>
- **紧急热线**: +1-************
- **在线支持**: https://support.example.com

## 🔄 恢复策略

### 服务恢复

```bash
# 重启服务
systemctl restart esign-mcp

# 如果systemd服务不可用
kill -9 $(pgrep -f "python main.py")
cd /app
python main.py &
```

### 配置恢复

```bash
# 恢复默认配置
cp config/test_env.yml.backup config/test_env.yml
cp config/mock_env.yml.backup config/mock_env.yml
```

### 完全重装

```bash
# 备份数据
cp -r config/ config_backup/
cp -r logs/ logs_backup/

# 重新克隆项目
git clone <repository-url> fresh-install
cd fresh-install

# 安装依赖
pip install -r requirements.txt

# 恢复配置
cp -r ../config_backup/ config/

# 启动服务
python main.py
```
