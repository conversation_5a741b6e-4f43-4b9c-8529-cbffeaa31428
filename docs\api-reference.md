# API参考文档

## 📋 目录

- [通用说明](#通用说明)
- [证书域API](#证书域api)
- [签署域API](#签署域api)
- [SaaS域API](#saas域api)
- [实名域API](#实名域api)
- [意愿域API](#意愿域api)
- [平台功能API](#平台功能api)

## 🔧 通用说明

### 基础信息

- **基础URL**: `http://localhost:8000`
- **API版本**: `v1`
- **API前缀**: `/api/v1`
- **内容类型**: `application/json`

### 通用参数

所有API接口都支持以下通用参数：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| environment | string | 否 | 环境描述，支持自然语言，如"使用模拟环境"、"测试环境" |

### 通用响应格式

#### 成功响应
```json
{
  "status": "success",
  "message": "操作成功",
  "timestamp": "2025-01-10T12:00:00.000Z",
  "data": {
    // 具体数据
  },
  "mcp_info": {
    "operation": "操作名称",
    "environment": "test",
    "platform": "esign-qa-mcp-platform",
    "version": "2.0.0"
  }
}
```

#### 错误响应
```json
{
  "status": "error",
  "message": "错误描述",
  "timestamp": "2025-01-10T12:00:00.000Z",
  "error_code": "ERROR_CODE",
  "details": {
    // 错误详情
  }
}
```

### 环境切换

支持的环境关键词：
- **测试环境**: "测试"、"test"、"开发"、"dev"
- **模拟环境**: "模拟"、"mock"、"仿真"、"虚拟"、"假"

## 🔐 证书域API

### 获取测试账号

**接口**: `POST /api/v1/certificate/get_test_account`

**描述**: 获取测试账号信息，包括手机号、证件号、姓名等

**MCP工具**: `certificate_get_test_account`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| environment | string | 否 | 环境类型 |

**响应示例**:
```json
{
  "status": "success",
  "data": {
    "phone": "***********",
    "idcard": "341321196102220130",
    "name": "测试东广政",
    "idcardType": "IDCARD",
    "orgCode": "91000000QLRGC4C61Q",
    "englishName": "Clarence Chad Price",
    "bankCard": "****************",
    "orgName": "esigntest东广政经营的个体工商户"
  }
}
```

### 创建证书

**接口**: `POST /api/v1/certificate/create_certificate`

**描述**: 创建数字证书

**MCP工具**: `certificate_create`

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| cert_name | string | 是 | - | 证书姓名 |
| phone | string | 是 | - | 手机号 |
| idcard | string | 是 | - | 身份证号 |
| algorithm | string | 否 | SM2 | 加密算法 |
| cert_time | string | 否 | ONEYEAR | 证书有效期 |
| app_id | string | 否 | - | 应用ID |
| environment | string | 否 | - | 环境类型 |

**响应示例**:
```json
{
  "status": "success",
  "data": {
    "certId": "cert_123456",
    "certName": "测试东广政",
    "status": "CREATED"
  }
}
```

### 查询证书详情

**接口**: `POST /api/v1/certificate/query_certificate_detail`

**MCP工具**: `certificate_query_detail`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| cert_id | string | 是 | 证书ID |
| app_id | string | 否 | 应用ID |
| environment | string | 否 | 环境类型 |

### 吊销证书

**接口**: `POST /api/v1/certificate/revoke_certificate`

**MCP工具**: `certificate_revoke`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| cert_info_id | string | 是 | 证书信息ID |
| environment | string | 否 | 环境类型 |

### 更新证书

**接口**: `POST /api/v1/certificate/update_certificate`

**MCP工具**: `certificate_update`

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| cert_info_id | string | 是 | - | 证书信息ID |
| cert_name | string | 是 | - | 证书姓名 |
| mobile | string | 是 | - | 手机号 |
| license_number | string | 是 | - | 证件号 |
| algorithm | string | 否 | SM2 | 加密算法 |
| cert_time | string | 否 | ONEYEAR | 证书有效期 |
| config_id | string | 否 | - | 配置ID |
| environment | string | 否 | - | 环境类型 |

### 获取用户信息

**接口**: `POST /api/v1/certificate/get_user_info`

**MCP工具**: `certificate_get_user_info`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | string | 是 | 用户ID |
| environment | string | 否 | 环境类型 |

## ✍️ 签署域API

### 创建签署流程

**接口**: `POST /api/v1/signing/create_signing_flow`

**MCP工具**: `signing_create_flow`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| flow_name | string | 是 | 流程名称 |
| document_content | string | 是 | 文档内容 |
| environment | string | 否 | 环境类型 |

### 添加签署人

**接口**: `POST /api/v1/signing/add_signer`

**MCP工具**: `signing_add_signer`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| flow_id | string | 是 | 流程ID |
| signer_name | string | 是 | 签署人姓名 |
| signer_mobile | string | 是 | 签署人手机号 |
| signer_idcard | string | 是 | 签署人身份证号 |
| environment | string | 否 | 环境类型 |

### 启动签署

**接口**: `POST /api/v1/signing/start_signing`

**MCP工具**: `signing_start`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| flow_id | string | 是 | 流程ID |
| environment | string | 否 | 环境类型 |

### 查询签署状态

**接口**: `POST /api/v1/signing/query_signing_status`

**MCP工具**: `signing_query_status`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| flow_id | string | 是 | 流程ID |
| environment | string | 否 | 环境类型 |

### 取消签署

**接口**: `POST /api/v1/signing/cancel_signing`

**MCP工具**: `signing_cancel`

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| flow_id | string | 是 | - | 流程ID |
| reason | string | 否 | 测试取消 | 取消原因 |
| environment | string | 否 | - | 环境类型 |

## 🏢 SaaS域API

### 注册个人账号

**接口**: `POST /api/v1/saas/register_test_person_account`

**MCP工具**: `saas_register_person_account`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| app_id | string | 是 | 应用ID |
| idNo | string | 是 | 身份证号 |
| mobile | string | 是 | 手机号 |
| name | string | 是 | 姓名 |
| thirdPartyUserId | string | 是 | 第三方用户ID |
| environment | string | 否 | 环境类型 |

### 注册企业账号

**接口**: `POST /api/v1/saas/register_test_company_account`

**MCP工具**: `saas_register_company_account`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| app_id | string | 是 | 应用ID |
| idNumber | string | 是 | 身份证号 |
| mobile | string | 是 | 手机号 |
| name | string | 是 | 姓名 |
| thirdPartyUserId | string | 是 | 第三方用户ID |
| orgLegalIdNumber | string | 是 | 企业法人身份证号 |
| orgLegalName | string | 是 | 企业法人姓名 |
| environment | string | 否 | 环境类型 |

### 创建组织

**接口**: `POST /api/v1/saas/create_organization`

**MCP工具**: `saas_create_organization`

### 添加组织成员

**接口**: `POST /api/v1/saas/add_member_to_org`

**MCP工具**: `saas_add_member_to_org`

### 查询组织信息

**接口**: `POST /api/v1/saas/query_org_info`

**MCP工具**: `saas_query_org_info`

## 🆔 实名域API

### 创建实名认证

**接口**: `POST /api/v1/identity/create_identity_verification`

**MCP工具**: `identity_create_verification`

### 查询认证状态

**接口**: `POST /api/v1/identity/query_verification_status`

**MCP工具**: `identity_query_verification_status`

### 上传身份材料

**接口**: `POST /api/v1/identity/upload_identity_materials`

**MCP工具**: `identity_upload_materials`

### 银行卡验证

**接口**: `POST /api/v1/identity/verify_bank_card`

**MCP工具**: `identity_verify_bank_card`

### 手机号验证

**接口**: `POST /api/v1/identity/verify_mobile`

**MCP工具**: `identity_verify_mobile`

## ✅ 意愿域API

### 创建意愿验证

**接口**: `POST /api/v1/intention/create_intention_verification`

**MCP工具**: `intention_create_verification`

### 查询意愿状态

**接口**: `POST /api/v1/intention/query_intention_status`

**MCP工具**: `intention_query_status`

### 确认意愿

**接口**: `POST /api/v1/intention/confirm_intention`

**MCP工具**: `intention_confirm`

### 取消意愿

**接口**: `POST /api/v1/intention/cancel_intention`

**MCP工具**: `intention_cancel`

### 获取意愿记录

**接口**: `POST /api/v1/intention/get_intention_record`

**MCP工具**: `intention_get_record`

## 🔧 平台功能API

### 环境管理

#### 获取环境信息
**接口**: `POST /api/v1/platform/get_environment_info`
**MCP工具**: `platform_get_environment_info`

#### 切换环境
**接口**: `POST /api/v1/platform/switch_environment`
**MCP工具**: `platform_switch_environment`

### 代码生成

#### 生成MCP代码
**接口**: `POST /api/v1/platform/generate_mcp_code`
**MCP工具**: `platform_generate_mcp_code`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| curl_command | string | 是 | curl命令或API描述 |
| domain | string | 否 | 业务域 |
| method_name | string | 否 | 方法名称 |

### 监控统计

#### 获取系统状态
**接口**: `POST /api/v1/platform/get_system_status`
**MCP工具**: `platform_get_system_status`

#### 获取API统计
**接口**: `POST /api/v1/platform/get_api_statistics`
**MCP工具**: `platform_get_api_statistics`

#### 健康检查
**接口**: `POST /api/v1/platform/get_health_check`
**MCP工具**: `platform_get_health_check`

#### 获取性能指标
**接口**: `POST /api/v1/platform/get_performance_metrics`
**MCP工具**: `platform_get_performance_metrics`

### 版本管理

#### 获取版本信息
**接口**: `POST /api/v1/platform/get_version_info`
**MCP工具**: `platform_get_version_info`

#### 获取更新日志
**接口**: `POST /api/v1/platform/get_changelog`
**MCP工具**: `platform_get_changelog`

#### 检查版本兼容性
**接口**: `POST /api/v1/platform/check_compatibility`
**MCP工具**: `platform_check_compatibility`

#### 获取API版本信息
**接口**: `POST /api/v1/platform/get_api_versions`
**MCP工具**: `platform_get_api_versions`

### 提示词管理

#### 获取提示词类型
**接口**: `POST /api/v1/platform/get_prompt_types`
**MCP工具**: `platform_get_prompt_types`

#### 获取提示词内容
**接口**: `POST /api/v1/platform/get_prompt_content`
**MCP工具**: `platform_get_prompt_content`

#### 搜索提示词
**接口**: `POST /api/v1/platform/search_prompts`
**MCP工具**: `platform_search_prompts`

## 📝 使用示例

### 完整流程示例

```python
# 1. 获取测试账号
account = certificate_get_test_account(environment="测试环境")

# 2. 创建数字证书
cert = certificate_create(
    cert_name=account["data"]["name"],
    phone=account["data"]["phone"],
    idcard=account["data"]["idcard"]
)

# 3. 创建签署流程
flow = signing_create_flow(
    flow_name="测试合同",
    document_content="这是一个测试合同"
)

# 4. 添加签署人
signer = signing_add_signer(
    flow_id=flow["data"]["flowId"],
    signer_name=account["data"]["name"],
    signer_mobile=account["data"]["phone"],
    signer_idcard=account["data"]["idcard"]
)

# 5. 启动签署
result = signing_start(flow_id=flow["data"]["flowId"])
```

## 🔍 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| ENV_001 | 环境配置不存在 | 检查环境配置文件 |
| API_001 | API调用失败 | 检查网络连接和API地址 |
| PARAM_001 | 参数验证失败 | 检查请求参数格式 |
| AUTH_001 | 认证失败 | 检查应用ID配置 |
| TIMEOUT_001 | 请求超时 | 检查网络状况或增加超时时间 |

## 📞 技术支持

- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health_check
- **MCP端点**: http://localhost:8000/mcp
- **问题反馈**: 通过Issue提交
