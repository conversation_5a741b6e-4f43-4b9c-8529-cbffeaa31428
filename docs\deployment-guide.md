# 部署指南

## 📋 目录

- [部署概述](#部署概述)
- [环境准备](#环境准备)
- [本地部署](#本地部署)
- [Docker部署](#docker部署)
- [生产环境部署](#生产环境部署)
- [监控和维护](#监控和维护)

## 🎯 部署概述

### 部署架构

```
┌─────────────────────────────────────────────────────────────┐
│                    负载均衡器                                │
│                   (Nginx/HAProxy)                          │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                  应用服务器                                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   实例1     │  │   实例2     │  │       实例N         │  │
│  │ Port 8000   │  │ Port 8001   │  │    Port 800N        │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                   外部服务                                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │  SDK服务    │  │  证书服务   │  │     基石服务        │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### 部署模式

1. **开发模式**: 单实例，用于开发和测试
2. **测试模式**: 多实例，用于集成测试
3. **生产模式**: 高可用，负载均衡，监控完整

## 🛠️ 环境准备

### 系统要求

#### 最低配置
- **CPU**: 2核
- **内存**: 4GB
- **磁盘**: 20GB
- **网络**: 100Mbps

#### 推荐配置
- **CPU**: 4核
- **内存**: 8GB
- **磁盘**: 50GB SSD
- **网络**: 1Gbps

### 软件依赖

```bash
# 系统更新
sudo apt update && sudo apt upgrade -y

# 安装Python 3.8+
sudo apt install python3.8 python3.8-venv python3.8-dev -y

# 安装系统依赖
sudo apt install build-essential curl wget git -y

# 安装Nginx (可选)
sudo apt install nginx -y

# 安装Docker (可选)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
```

## 🏠 本地部署

### 快速部署

```bash
# 1. 克隆项目
git clone <repository-url>
cd esign-qa-mcp-service

# 2. 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置环境
cp config/test_env.yml.example config/test_env.yml
cp config/mock_env.yml.example config/mock_env.yml

# 5. 启动服务
python main.py
```

### 配置文件设置

#### 1. 环境配置

编辑 `config/test_env.yml`:
```yaml
environment: test
description: "测试环境配置"
api_urls:
  sdk: "http://sdk.testk8s.tsign.cn"
  cert: "http://cert-service.testk8s.tsign.cn"
  footstone: "http://in-test-openapi.tsign.cn"
app_ids:
  default: "your_app_id"
  cert: "your_cert_app_id"
```

#### 2. 应用配置

创建 `.env` 文件:
```bash
# 服务配置
HOST=0.0.0.0
PORT=8000
API_V1_STR=/api/v1

# 日志配置
LOG_LEVEL=INFO

# 环境配置
DEFAULT_ENVIRONMENT=test

# AI模型配置
DEEPSEEK_API_KEY=your_api_key
DEEPSEEK_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
DEEPSEEK_MODEL=qwen-turbo
```

### 服务验证

```bash
# 健康检查
curl http://localhost:8000/health_check

# API文档
curl http://localhost:8000/docs

# MCP端点
curl http://localhost:8000/mcp

# 测试API
curl -X POST http://localhost:8000/api/v1/certificate/get_test_account \
  -H "Content-Type: application/json" \
  -d '{}'
```

## 🐳 Docker部署

### Dockerfile

```dockerfile
FROM python:3.8-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/health_check || exit 1

# 启动命令
CMD ["python", "main.py"]
```

### Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  esign-mcp:
    build: .
    ports:
      - "8000:8000"
    environment:
      - HOST=0.0.0.0
      - PORT=8000
      - LOG_LEVEL=INFO
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health_check"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - esign-mcp
    restart: unless-stopped

volumes:
  logs:
```

### 部署命令

```bash
# 构建镜像
docker build -t esign-mcp:latest .

# 运行容器
docker run -d \
  --name esign-mcp \
  -p 8000:8000 \
  -v $(pwd)/config:/app/config \
  -v $(pwd)/logs:/app/logs \
  esign-mcp:latest

# 使用Docker Compose
docker-compose up -d

# 查看日志
docker-compose logs -f esign-mcp

# 停止服务
docker-compose down
```

## 🚀 生产环境部署

### 1. 使用Gunicorn

#### 安装Gunicorn

```bash
pip install gunicorn uvicorn[standard]
```

#### Gunicorn配置

创建 `gunicorn.conf.py`:
```python
# Gunicorn配置文件
bind = "0.0.0.0:8000"
workers = 4
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True

# 日志配置
accesslog = "/app/logs/access.log"
errorlog = "/app/logs/error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 进程配置
user = "appuser"
group = "appuser"
tmp_upload_dir = None
secure_scheme_headers = {
    'X-FORWARDED-PROTOCOL': 'ssl',
    'X-FORWARDED-PROTO': 'https',
    'X-FORWARDED-SSL': 'on'
}
```

#### 启动命令

```bash
# 启动Gunicorn
gunicorn -c gunicorn.conf.py main:create_app()

# 使用systemd管理
sudo systemctl start esign-mcp
sudo systemctl enable esign-mcp
```

### 2. Nginx配置

创建 `/etc/nginx/sites-available/esign-mcp`:
```nginx
upstream esign_mcp {
    server 127.0.0.1:8000;
    server 127.0.0.1:8001;
    server 127.0.0.1:8002;
    server 127.0.0.1:8003;
}

server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL配置
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
    
    # 日志配置
    access_log /var/log/nginx/esign-mcp.access.log;
    error_log /var/log/nginx/esign-mcp.error.log;
    
    # 代理配置
    location / {
        proxy_pass http://esign_mcp;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # 静态文件
    location /static/ {
        alias /app/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 健康检查
    location /health_check {
        proxy_pass http://esign_mcp;
        access_log off;
    }
}
```

### 3. Systemd服务

创建 `/etc/systemd/system/esign-mcp.service`:
```ini
[Unit]
Description=E-Sign QA MCP Service
After=network.target

[Service]
Type=exec
User=appuser
Group=appuser
WorkingDirectory=/app
Environment=PATH=/app/venv/bin
ExecStart=/app/venv/bin/gunicorn -c gunicorn.conf.py main:create_app()
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=10

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/app/logs /app/config

[Install]
WantedBy=multi-user.target
```

启动服务:
```bash
sudo systemctl daemon-reload
sudo systemctl start esign-mcp
sudo systemctl enable esign-mcp
sudo systemctl status esign-mcp
```

### 4. 高可用部署

#### 多实例部署

```bash
# 启动多个实例
gunicorn -c gunicorn.conf.py -b 0.0.0.0:8000 main:create_app()
gunicorn -c gunicorn.conf.py -b 0.0.0.0:8001 main:create_app()
gunicorn -c gunicorn.conf.py -b 0.0.0.0:8002 main:create_app()
gunicorn -c gunicorn.conf.py -b 0.0.0.0:8003 main:create_app()
```

#### 负载均衡配置

```nginx
upstream esign_mcp {
    least_conn;
    server 127.0.0.1:8000 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:8001 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:8002 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:8003 weight=1 max_fails=3 fail_timeout=30s;
}
```

## 📊 监控和维护

### 1. 日志管理

#### 日志配置

```python
# logging.conf
[loggers]
keys=root,uvicorn,gunicorn

[handlers]
keys=console,file,rotating

[formatters]
keys=generic

[logger_root]
level=INFO
handlers=console,rotating

[logger_uvicorn]
level=INFO
handlers=console,file
qualname=uvicorn
propagate=0

[logger_gunicorn]
level=INFO
handlers=console,file
qualname=gunicorn
propagate=0

[handler_console]
class=StreamHandler
formatter=generic
args=(sys.stdout,)

[handler_file]
class=FileHandler
formatter=generic
args=('/app/logs/app.log',)

[handler_rotating]
class=handlers.RotatingFileHandler
formatter=generic
args=('/app/logs/app.log', 'a', 10485760, 5)

[formatter_generic]
format=%(asctime)s [%(process)d] [%(levelname)s] %(name)s: %(message)s
datefmt=%Y-%m-%d %H:%M:%S
```

#### 日志轮转

```bash
# /etc/logrotate.d/esign-mcp
/app/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 appuser appuser
    postrotate
        systemctl reload esign-mcp
    endscript
}
```

### 2. 监控配置

#### Prometheus监控

```python
# 添加Prometheus指标
from prometheus_client import Counter, Histogram, generate_latest

REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('http_request_duration_seconds', 'HTTP request duration')

@app.middleware("http")
async def prometheus_middleware(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    duration = time.time() - start_time
    
    REQUEST_COUNT.labels(method=request.method, endpoint=request.url.path).inc()
    REQUEST_DURATION.observe(duration)
    
    return response

@app.get("/metrics")
async def metrics():
    return Response(generate_latest(), media_type="text/plain")
```

#### 健康检查增强

```python
@app.get("/health_check")
async def health_check():
    checks = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": settings.VERSION,
        "checks": {
            "database": check_database(),
            "external_apis": check_external_apis(),
            "disk_space": check_disk_space(),
            "memory": check_memory()
        }
    }
    
    # 如果任何检查失败，返回503
    if any(not check["healthy"] for check in checks["checks"].values()):
        return JSONResponse(content=checks, status_code=503)
    
    return checks
```

### 3. 备份策略

#### 配置备份

```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup/esign-mcp"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR/$DATE

# 备份配置文件
cp -r /app/config $BACKUP_DIR/$DATE/
cp -r /app/logs $BACKUP_DIR/$DATE/

# 备份数据库（如果有）
# mysqldump -u user -p database > $BACKUP_DIR/$DATE/database.sql

# 压缩备份
tar -czf $BACKUP_DIR/esign-mcp-$DATE.tar.gz -C $BACKUP_DIR $DATE

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "备份完成: $BACKUP_DIR/esign-mcp-$DATE.tar.gz"
```

#### 自动备份

```bash
# 添加到crontab
crontab -e

# 每天凌晨2点备份
0 2 * * * /app/scripts/backup.sh
```

### 4. 性能优化

#### 系统优化

```bash
# 调整系统参数
echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 65535' >> /etc/sysctl.conf
echo 'fs.file-max = 100000' >> /etc/sysctl.conf

# 应用配置
sysctl -p
```

#### 应用优化

```python
# 连接池配置
import httpx

http_client = httpx.AsyncClient(
    limits=httpx.Limits(
        max_keepalive_connections=20,
        max_connections=100
    ),
    timeout=httpx.Timeout(30.0)
)
```

## 🔧 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查日志
   journalctl -u esign-mcp -f
   
   # 检查端口占用
   netstat -tlnp | grep 8000
   ```

2. **API响应慢**
   ```bash
   # 检查系统资源
   top
   iostat
   
   # 检查网络连接
   netstat -an | grep ESTABLISHED | wc -l
   ```

3. **内存泄漏**
   ```bash
   # 监控内存使用
   ps aux | grep gunicorn
   
   # 重启服务
   systemctl restart esign-mcp
   ```

### 监控脚本

```bash
#!/bin/bash
# monitor.sh

# 检查服务状态
if ! systemctl is-active --quiet esign-mcp; then
    echo "服务已停止，正在重启..."
    systemctl restart esign-mcp
fi

# 检查健康状态
if ! curl -f http://localhost:8000/health_check > /dev/null 2>&1; then
    echo "健康检查失败，正在重启..."
    systemctl restart esign-mcp
fi

# 检查磁盘空间
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "磁盘空间不足: ${DISK_USAGE}%"
    # 清理日志
    find /app/logs -name "*.log" -mtime +7 -delete
fi
```

## 📞 技术支持

### 部署支持

- **文档**: 查看完整部署文档
- **日志**: 检查应用和系统日志
- **监控**: 使用监控工具诊断问题
- **社区**: 通过Issue获取帮助

### 紧急联系

- **服务异常**: 检查健康检查端点
- **性能问题**: 查看监控指标
- **安全问题**: 立即联系安全团队
- **数据问题**: 使用备份恢复数据
