#!/usr/bin/env python3
"""
代码生成器 - 自动生成MCP代码
支持从curl命令、API描述等自动生成MCP代码
"""
import re
import logging
from typing import Dict, Any, Optional
from mcpService.common.mcp_template import mcp_template
from mcpService.common.response_formatter import formatter

logger = logging.getLogger(__name__)


def detect_mcp_trigger(text: str) -> bool:
    """
    检测是否包含MCP接入触发关键词
    
    Args:
        text: 输入文本
        
    Returns:
        是否包含触发关键词
    """
    trigger_keywords = [
        "接入mcp", "接入MCP", "mcp接入", "MCP接入",
        "生成mcp", "生成MCP", "mcp生成", "MCP生成",
        "创建mcp", "创建MCP", "mcp创建", "MCP创建"
    ]
    
    text_lower = text.lower()
    for keyword in trigger_keywords:
        if keyword.lower() in text_lower:
            return True
    
    return False


def extract_curl_from_text(text: str) -> Optional[str]:
    """
    从文本中提取curl命令
    
    Args:
        text: 输入文本
        
    Returns:
        提取的curl命令，如果没有则返回None
    """
    # 匹配curl命令的正则表达式
    curl_patterns = [
        r"curl\s+.*?(?=\n\n|\n$|$)",  # 基本curl命令
        r"curl\s+.*?(?=\n[^\\]|\n$|$)",  # 考虑换行符的curl命令
    ]
    
    for pattern in curl_patterns:
        match = re.search(pattern, text, re.DOTALL | re.MULTILINE)
        if match:
            curl_command = match.group(0).strip()
            # 清理换行符和多余空格
            curl_command = re.sub(r'\\\s*\n\s*', ' ', curl_command)
            curl_command = re.sub(r'\s+', ' ', curl_command)
            return curl_command
    
    return None


def generate_mcp_code_from_curl(
    input_text: str,
    domain: Optional[str] = None,
    method_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    从curl命令生成MCP代码
    
    Args:
        input_text: 输入文本，包含curl命令
        domain: 业务域
        method_name: 方法名称
        
    Returns:
        生成结果
    """
    try:
        # 检测是否包含MCP触发关键词
        if not detect_mcp_trigger(input_text):
            return formatter.error(
                message="未检测到MCP接入触发关键词",
                details={
                    "trigger_keywords": ["接入mcp", "生成mcp", "创建mcp"],
                    "input_text": input_text[:200] + "..." if len(input_text) > 200 else input_text
                }
            )
        
        # 提取curl命令
        curl_command = extract_curl_from_text(input_text)
        if not curl_command:
            return formatter.error(
                message="未在输入文本中找到curl命令",
                details={
                    "input_text": input_text[:200] + "..." if len(input_text) > 200 else input_text,
                    "help": "请确保输入包含完整的curl命令"
                }
            )
        
        logger.info(f"提取到curl命令: {curl_command}")
        
        # 使用MCP模板生成代码
        result = mcp_template.generate_complete_mcp_code(
            curl_command=curl_command,
            domain=domain,
            method_name=method_name
        )
        
        if result.get("status") == "error":
            return result
        
        # 添加额外信息
        result["data"]["curl_command"] = curl_command
        result["data"]["auto_generated"] = True
        result["data"]["generation_time"] = "2025-01-10"
        
        return formatter.success(
            data=result["data"],
            message="MCP代码自动生成成功",
            extra_info={
                "trigger_detected": True,
                "curl_extracted": True,
                "generation_method": "auto_from_curl"
            }
        )
        
    except Exception as e:
        logger.error(f"自动生成MCP代码失败: {str(e)}")
        return formatter.error(
            message=f"自动生成MCP代码失败: {str(e)}",
            details={
                "input_text": input_text[:200] + "..." if len(input_text) > 200 else input_text
            }
        )


def generate_mcp_code_from_api_spec(
    api_spec: Dict[str, Any],
    domain: Optional[str] = None,
    method_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    从API规范生成MCP代码
    
    Args:
        api_spec: API规范字典
        domain: 业务域
        method_name: 方法名称
        
    Returns:
        生成结果
    """
    try:
        # 构建curl命令
        url = api_spec.get("url", "")
        method = api_spec.get("method", "POST")
        headers = api_spec.get("headers", {})
        data = api_spec.get("data", {})
        
        # 构建curl命令字符串
        curl_parts = ["curl"]
        
        # 添加URL
        curl_parts.append(f"'{url}'")
        
        # 添加方法
        if method.upper() != "GET":
            curl_parts.append(f"--request {method.upper()}")
        
        # 添加请求头
        for key, value in headers.items():
            curl_parts.append(f"--header '{key}: {value}'")
        
        # 添加数据
        if data and method.upper() != "GET":
            import json
            data_str = json.dumps(data, ensure_ascii=False)
            curl_parts.append(f"--data-raw '{data_str}'")
        
        curl_command = " \\\n".join(curl_parts)
        
        # 使用curl命令生成代码
        return mcp_template.generate_complete_mcp_code(
            curl_command=curl_command,
            domain=domain,
            method_name=method_name
        )
        
    except Exception as e:
        logger.error(f"从API规范生成MCP代码失败: {str(e)}")
        return formatter.error(
            message=f"从API规范生成MCP代码失败: {str(e)}",
            details={"api_spec": api_spec}
        )


def generate_mcp_code_interactive(
    description: str,
    url: str,
    method: str = "POST",
    parameters: Optional[Dict[str, Any]] = None,
    domain: Optional[str] = None
) -> Dict[str, Any]:
    """
    交互式生成MCP代码
    
    Args:
        description: 接口描述
        url: 接口地址
        method: 请求方法
        parameters: 参数字典
        domain: 业务域
        
    Returns:
        生成结果
    """
    try:
        # 构建API规范
        api_spec = {
            "url": url,
            "method": method,
            "headers": {
                "Content-Type": "application/json"
            },
            "data": parameters or {}
        }
        
        # 从描述中提取方法名
        method_name = re.sub(r"[^a-zA-Z0-9_\u4e00-\u9fa5]", "_", description)
        method_name = re.sub(r"_+", "_", method_name).strip("_")
        
        # 生成代码
        result = generate_mcp_code_from_api_spec(
            api_spec=api_spec,
            domain=domain,
            method_name=method_name
        )
        
        if result.get("status") == "success":
            result["data"]["description"] = description
            result["data"]["interactive_generated"] = True
        
        return result
        
    except Exception as e:
        logger.error(f"交互式生成MCP代码失败: {str(e)}")
        return formatter.error(
            message=f"交互式生成MCP代码失败: {str(e)}",
            details={
                "description": description,
                "url": url,
                "method": method,
                "parameters": parameters
            }
        )
