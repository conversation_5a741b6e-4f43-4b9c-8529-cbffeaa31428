#!/usr/bin/env python3
"""
环境服务 - 环境管理相关功能
提供环境信息查询、环境切换等功能
"""
import logging
from typing import Dict, Any, Optional
from mcpService.common.environment_manager import env_manager
from mcpService.common.response_formatter import formatter

logger = logging.getLogger(__name__)


def get_environment_info(environment: Optional[str] = None) -> Dict[str, Any]:
    """
    获取环境信息
    
    Args:
        environment: 环境描述，支持自然语言
        
    Returns:
        环境信息
    """
    try:
        # 如果指定了环境，先检测和设置
        if environment:
            detected_env = env_manager.detect_environment(environment)
            logger.info(f"检测到环境: {detected_env}")
        
        # 获取当前环境配置
        current_config = env_manager.get_environment_config()
        
        # 获取所有可用环境
        test_config = env_manager.get_environment_config("test")
        mock_config = env_manager.get_environment_config("mock")
        
        return formatter.success(
            data={
                "current_environment": env_manager.current_env,
                "current_config": current_config,
                "available_environments": {
                    "test": {
                        "name": "测试环境",
                        "description": test_config.get("description", ""),
                        "api_urls": test_config.get("api_urls", {}),
                        "app_ids": test_config.get("app_ids", {})
                    },
                    "mock": {
                        "name": "模拟环境", 
                        "description": mock_config.get("description", ""),
                        "api_urls": mock_config.get("api_urls", {}),
                        "app_ids": mock_config.get("app_ids", {})
                    }
                },
                "business_domains": current_config.get("business_domains", {}),
                "environment_detection": {
                    "input": environment,
                    "detected": env_manager.detect_environment(environment) if environment else None,
                    "keywords": {
                        "test": ["测试", "test", "开发", "dev"],
                        "mock": ["模拟", "mock", "仿真", "虚拟", "假"]
                    }
                }
            },
            message=f"当前环境: {env_manager.current_env}"
        )
        
    except Exception as e:
        logger.error(f"获取环境信息失败: {str(e)}")
        return formatter.error(
            message=f"获取环境信息失败: {str(e)}",
            details={"environment": environment}
        )


def switch_environment(environment: str) -> Dict[str, Any]:
    """
    切换环境
    
    Args:
        environment: 目标环境
        
    Returns:
        切换结果
    """
    try:
        # 记录切换前的环境
        previous_env = env_manager.current_env
        
        # 执行环境切换
        result = env_manager.switch_environment(environment)
        
        if result.get("status") == "success":
            return formatter.success(
                data={
                    "previous_environment": previous_env,
                    "current_environment": environment,
                    "config": result["data"]["config"],
                    "switch_time": "2025-01-10",
                    "affected_services": [
                        "API请求地址",
                        "应用ID配置", 
                        "请求头配置",
                        "业务域配置"
                    ]
                },
                message=f"环境已从 {previous_env} 切换到 {environment}"
            )
        else:
            return formatter.error(
                message=result.get("message", "环境切换失败"),
                details=result
            )
            
    except Exception as e:
        logger.error(f"切换环境失败: {str(e)}")
        return formatter.error(
            message=f"切换环境失败: {str(e)}",
            details={"target_environment": environment}
        )


def get_domain_environment_info(domain: str, environment: Optional[str] = None) -> Dict[str, Any]:
    """
    获取业务域环境信息
    
    Args:
        domain: 业务域名称
        environment: 环境描述
        
    Returns:
        业务域环境信息
    """
    try:
        # 获取业务域配置
        domain_config = env_manager.get_domain_config(domain, environment)
        
        if not domain_config:
            return formatter.error(
                message=f"未找到业务域 {domain} 的配置",
                details={"domain": domain, "environment": environment}
            )
        
        # 获取相关的API地址和应用ID
        api_url = env_manager.get_api_url("sdk", environment)
        app_id = env_manager.get_app_id("default", environment)
        headers = env_manager.get_headers(environment)
        
        return formatter.success(
            data={
                "domain": domain,
                "domain_config": domain_config,
                "environment": env_manager.current_env,
                "api_info": {
                    "base_url": domain_config.get("base_url", api_url),
                    "app_id": domain_config.get("app_id", app_id),
                    "headers": headers
                },
                "usage_example": {
                    "curl": f"curl -X POST '{domain_config.get('base_url', api_url)}/api/path' "
                           f"-H 'X-Tsign-Open-App-Id: {domain_config.get('app_id', app_id)}' "
                           f"-H 'Content-Type: application/json' "
                           f"-d '{{}}'",
                    "mcp_call": f"通过MCP调用 {domain} 域的相关工具"
                }
            },
            message=f"业务域 {domain} 环境信息获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取业务域环境信息失败: {str(e)}")
        return formatter.error(
            message=f"获取业务域环境信息失败: {str(e)}",
            details={"domain": domain, "environment": environment}
        )


def validate_environment_config(environment: str) -> Dict[str, Any]:
    """
    验证环境配置
    
    Args:
        environment: 环境名称
        
    Returns:
        验证结果
    """
    try:
        config = env_manager.get_environment_config(environment)
        
        if not config:
            return formatter.error(
                message=f"环境 {environment} 配置不存在"
            )
        
        # 验证必要字段
        required_fields = ["api_urls", "app_ids", "headers", "business_domains"]
        missing_fields = []
        
        for field in required_fields:
            if field not in config:
                missing_fields.append(field)
        
        # 验证API地址可访问性（这里只做格式检查）
        api_urls = config.get("api_urls", {})
        invalid_urls = []
        
        for service, url in api_urls.items():
            if not url or not url.startswith("http"):
                invalid_urls.append(f"{service}: {url}")
        
        # 生成验证报告
        validation_result = {
            "environment": environment,
            "config_exists": True,
            "missing_fields": missing_fields,
            "invalid_urls": invalid_urls,
            "business_domains_count": len(config.get("business_domains", {})),
            "is_valid": len(missing_fields) == 0 and len(invalid_urls) == 0
        }
        
        if validation_result["is_valid"]:
            return formatter.success(
                data=validation_result,
                message=f"环境 {environment} 配置验证通过"
            )
        else:
            return formatter.error(
                message=f"环境 {environment} 配置验证失败",
                details=validation_result
            )
            
    except Exception as e:
        logger.error(f"验证环境配置失败: {str(e)}")
        return formatter.error(
            message=f"验证环境配置失败: {str(e)}",
            details={"environment": environment}
        )
