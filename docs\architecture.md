# 架构设计文档

## 📋 目录

- [总体架构](#总体架构)
- [分层架构](#分层架构)
- [业务域设计](#业务域设计)
- [环境管理架构](#环境管理架构)
- [MCP协议集成](#mcp协议集成)
- [数据流设计](#数据流设计)
- [扩展性设计](#扩展性设计)

## 🏗️ 总体架构

### 架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                    AI工具 (lingma等)                        │
└─────────────────────┬───────────────────────────────────────┘
                      │ MCP协议
┌─────────────────────▼───────────────────────────────────────┐
│                  MCP协议层                                   │
│              (FastApiMCP)                                   │
└─────────────────────┬───────────────────────────────────────┘
                      │ HTTP/JSON
┌─────────────────────▼───────────────────────────────────────┐
│                   控制器层                                   │
│  ┌─────────┬─────────┬─────────┬─────────┬─────────┬───────┐ │
│  │ 签署域  │ SaaS域  │ 实名域  │ 意愿域  │ 证书域  │平台功能│ │
│  └─────────┴─────────┴─────────┴─────────┴─────────┴───────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                   服务层                                     │
│  ┌─────────┬─────────┬─────────┬─────────┬─────────┬───────┐ │
│  │签署服务 │SaaS服务 │实名服务 │意愿服务 │证书服务 │平台服务│ │
│  └─────────┴─────────┴─────────┴─────────┴─────────┴───────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                   公共层                                     │
│  ┌─────────┬─────────┬─────────┬─────────┬─────────────────┐ │
│  │基础服务 │HTTP客户端│环境管理 │响应格式化│MCP模板生成器   │ │
│  └─────────┴─────────┴─────────┴─────────┴─────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                  外部服务                                    │
│  ┌─────────┬─────────┬─────────┬─────────┬─────────────────┐ │
│  │SDK服务  │证书服务 │基石服务 │其他服务 │     ...         │ │
│  └─────────┴─────────┴─────────┴─────────┴─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

1. **MCP协议层**：负责与AI工具的协议通信
2. **控制器层**：按业务域组织的API接口
3. **服务层**：业务逻辑实现
4. **公共层**：通用工具和基础设施
5. **外部服务**：第三方API服务

## 🏛️ 分层架构

### 1. 控制器层 (Controller Layer)

**职责**：
- API接口定义和路由
- 请求参数验证
- 响应格式统一
- MCP工具暴露

**设计原则**：
- 按业务域划分控制器
- 统一的接口命名规范
- 支持环境参数传递
- 标准化的错误处理

**文件结构**：
```
app/mcpController/domains/
├── signing_controller.py      # 签署域控制器
├── saas_controller.py         # SaaS域控制器
├── identity_controller.py     # 实名域控制器
├── intention_controller.py    # 意愿域控制器
├── certificate_controller.py  # 证书域控制器
└── platform_controller.py     # 平台功能控制器
```

### 2. 服务层 (Service Layer)

**职责**：
- 业务逻辑实现
- 外部API调用
- 数据转换和处理
- 错误处理和重试

**设计原则**：
- 继承统一的基础服务类
- 环境感知的API调用
- 标准化的响应格式
- 可扩展的业务逻辑

**文件结构**：
```
mcpService/domains/
├── signing_service.py      # 签署域服务
├── saas_service.py         # SaaS域服务
├── identity_service.py     # 实名域服务
├── intention_service.py    # 意愿域服务
└── certificate_service.py  # 证书域服务
```

### 3. 公共层 (Common Layer)

**职责**：
- 通用工具类
- 基础设施组件
- 配置管理
- 模板生成

**核心组件**：

#### BaseService (基础服务类)
```python
class BaseService:
    def __init__(self, domain: str)
    def get_domain_config(self, environment: Optional[str] = None)
    def make_api_request(self, path: str, data: Dict, ...)
    def format_response(self, data: Any, operation: str, ...)
```

#### HttpClient (HTTP客户端)
```python
class HttpClient:
    def make_request(self, url: str, method: str, ...)
    # 支持环境切换、请求日志、错误处理
```

#### EnvironmentManager (环境管理器)
```python
class EnvironmentManager:
    def detect_environment(self, text: Optional[str] = None)
    def get_environment_config(self, env: Optional[str] = None)
    def switch_environment(self, env: str)
```

## 🎯 业务域设计

### 业务域划分原则

1. **功能内聚**：相关功能归属同一域
2. **低耦合**：域之间依赖最小化
3. **可扩展**：支持新域的添加
4. **独立部署**：每个域可独立开发和部署

### 域间交互设计

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   签署域    │    │   证书域    │    │   实名域    │
│             │◄──►│             │◄──►│             │
│ 签署流程管理 │    │ 证书管理    │    │ 实名认证    │
└─────────────┘    └─────────────┘    └─────────────┘
       ▲                   ▲                   ▲
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   SaaS域    │    │   意愿域    │    │   平台功能  │
│             │    │             │    │             │
│ 账号组织管理 │    │ 意愿验证    │    │ 环境/监控   │
└─────────────┘    └─────────────┘    └─────────────┘
```

### 域配置管理

每个业务域都有独立的配置：

```yaml
business_domains:
  signing:
    name: "签署域"
    description: "电子签名相关功能"
    app_id: "7876722740"
    base_url: "http://sdk.testk8s.tsign.cn"
  
  certificate:
    name: "证书域"
    description: "数字证书相关功能"
    app_id: "1111564052"
    base_url: "http://cert-service.testk8s.tsign.cn"
```

## 🌐 环境管理架构

### 环境抽象层

```
┌─────────────────────────────────────────────────────────────┐
│                    环境抽象层                                │
│  ┌─────────────────┐              ┌─────────────────────────┐ │
│  │  自然语言检测   │              │     环境配置管理        │ │
│  │                 │              │                         │ │
│  │ "使用模拟环境"  │─────────────►│  test_env.yml          │ │
│  │ "测试环境"      │              │  mock_env.yml          │ │
│  │ "mock"          │              │                         │ │
│  └─────────────────┘              └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    配置应用层                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │  API地址    │  │  应用ID     │  │     请求头          │  │
│  │  配置       │  │  配置       │  │     配置            │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### 环境切换流程

1. **检测阶段**：从输入文本中检测环境关键词
2. **配置阶段**：加载对应环境的配置文件
3. **应用阶段**：将配置应用到HTTP请求中
4. **验证阶段**：验证环境配置的有效性

### 配置文件结构

```yaml
environment: test
description: "测试环境配置"
api_urls:
  sdk: "http://sdk.testk8s.tsign.cn"
  cert: "http://cert-service.testk8s.tsign.cn"
app_ids:
  default: "7876611670"
  cert: "1111564052"
headers:
  Content-Type: "application/json"
  X-Tsign-Open-Auth-Mode: "simple"
business_domains:
  signing:
    name: "签署域"
    app_id: "7876722740"
    base_url: "http://sdk.testk8s.tsign.cn"
```

## 🔌 MCP协议集成

### MCP架构层次

```
┌─────────────────────────────────────────────────────────────┐
│                    AI工具层                                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   lingma    │  │   Claude    │  │     其他AI工具      │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────┬───────────────────────────────────────┘
                      │ MCP协议
┌─────────────────────▼───────────────────────────────────────┐
│                  MCP服务层                                   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              FastApiMCP                                 │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │ │
│  │  │  工具发现   │  │  参数验证   │  │    调用路由     │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                  业务逻辑层                                  │
│  ┌─────────┬─────────┬─────────┬─────────┬─────────┬───────┐ │
│  │ 签署域  │ SaaS域  │ 实名域  │ 意愿域  │ 证书域  │平台功能│ │
│  └─────────┴─────────┴─────────┴─────────┴─────────┴───────┘ │
└─────────────────────────────────────────────────────────────┘
```

### MCP工具映射

每个API接口自动映射为MCP工具：

```python
# API接口
@certificate_router.post("/get_test_account")
async def get_test_account_endpoint(environment: Optional[str] = None):
    pass

# 自动生成的MCP工具
# 工具名称: certificate_get_test_account
# 参数: environment (可选)
# 描述: 获取测试账号信息
```

### 工具命名规范

- **格式**：`{domain}_{operation}`
- **域名映射**：
  - signing → signing
  - saas → saas
  - identity → identity
  - intention → intention
  - certificate → certificate
  - platform → platform

## 📊 数据流设计

### 请求处理流程

```
AI工具请求 → MCP协议解析 → 路由分发 → 参数验证 → 环境检测 → 
服务调用 → 外部API → 响应处理 → 格式化 → MCP响应 → AI工具
```

### 详细数据流

1. **请求接收**：
   ```json
   {
     "method": "tools/call",
     "params": {
       "name": "certificate_get_test_account",
       "arguments": {
         "environment": "使用模拟环境"
       }
     }
   }
   ```

2. **环境检测**：
   ```python
   detected_env = env_manager.detect_environment("使用模拟环境")
   # 结果: "mock"
   ```

3. **配置获取**：
   ```python
   config = env_manager.get_environment_config("mock")
   # 加载 mock_env.yml 配置
   ```

4. **API调用**：
   ```python
   result = http_client.make_request(
       url="http://mock-sdk.tsign.cn/random/get",
       headers={"X-Tsign-Open-App-Id": "mock_app_id"},
       environment="mock"
   )
   ```

5. **响应格式化**：
   ```json
   {
     "status": "success",
     "data": {...},
     "message": "获取测试账号成功",
     "timestamp": "2025-01-10T...",
     "mcp_info": {
       "operation": "获取测试账号",
       "environment": "mock",
       "platform": "esign-qa-mcp-platform"
     }
   }
   ```

## 🔧 扩展性设计

### 新业务域接入

1. **创建域服务**：
   ```python
   # mcpService/domains/new_domain_service.py
   class NewDomainService(BaseService):
       def __init__(self):
           super().__init__(domain="new_domain")
   ```

2. **创建域控制器**：
   ```python
   # app/mcpController/domains/new_domain_controller.py
   new_domain_router = APIRouter(
       prefix="/new_domain",
       tags=["新业务域"]
   )
   ```

3. **更新配置**：
   ```yaml
   # config/test_env.yml
   business_domains:
     new_domain:
       name: "新业务域"
       app_id: "new_app_id"
       base_url: "http://new-service.tsign.cn"
   ```

4. **注册路由**：
   ```python
   # main.py
   from app.mcpController.domains import new_domain_router
   app.include_router(new_domain_router, prefix=f"{settings.API_V1_STR}")
   ```

### 新环境支持

1. **创建环境配置**：
   ```yaml
   # config/prod_env.yml
   environment: prod
   description: "生产环境配置"
   api_urls:
     sdk: "http://sdk.tsign.cn"
   ```

2. **更新环境检测**：
   ```python
   def detect_environment_from_text(self, text: str) -> str:
       # 添加新环境关键词检测
       prod_keywords = ["生产", "prod", "正式"]
   ```

### 新功能模块

1. **平台服务扩展**：
   ```python
   # mcpService/platform/new_feature_service.py
   def new_feature_function():
       pass
   ```

2. **控制器接口**：
   ```python
   # app/mcpController/domains/platform_controller.py
   @platform_router.post("/new_feature")
   async def new_feature_endpoint():
       return new_feature_function()
   ```

### 监控和日志扩展

1. **自定义监控指标**：
   ```python
   monitoring_service.add_custom_metric("custom_metric", value)
   ```

2. **日志格式扩展**：
   ```python
   logger.info("自定义日志", extra={
       "domain": "new_domain",
       "operation": "custom_operation",
       "environment": "test"
   })
   ```

## 🔒 安全性设计

### 认证和授权

- **API密钥管理**：统一的应用ID配置
- **环境隔离**：不同环境使用不同的服务地址
- **请求验证**：参数验证和格式检查

### 数据安全

- **敏感信息脱敏**：日志中的敏感数据处理
- **配置文件保护**：环境配置文件的安全存储
- **传输加密**：HTTPS协议支持

## 📈 性能优化

### 缓存策略

- **配置缓存**：环境配置的内存缓存
- **响应缓存**：常用API响应的缓存
- **连接池**：HTTP连接复用

### 异步处理

- **异步API**：FastAPI的异步支持
- **并发控制**：请求并发数限制
- **超时管理**：请求超时配置

## 🔄 容错和恢复

### 错误处理

- **分级错误处理**：系统级、业务级错误分类
- **重试机制**：失败请求的自动重试
- **降级策略**：服务不可用时的降级处理

### 监控和告警

- **健康检查**：系统健康状态监控
- **性能监控**：API响应时间统计
- **错误统计**：错误率和错误类型统计

## 📋 总结

本架构设计文档详细描述了E-Sign QA MCP造数平台的整体架构，包括：

1. **分层架构**：清晰的职责分离和模块化设计
2. **业务域划分**：按功能领域组织的可扩展架构
3. **环境管理**：灵活的多环境支持和自然语言切换
4. **MCP集成**：与AI工具的无缝协议集成
5. **扩展性**：支持新域、新环境、新功能的快速接入
6. **安全性**：完善的安全措施和数据保护
7. **性能优化**：缓存、异步、连接池等优化策略
8. **容错恢复**：健壮的错误处理和监控机制

该架构为平台的长期发展和维护提供了坚实的基础。
