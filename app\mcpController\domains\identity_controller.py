#!/usr/bin/env python3
"""
实名域控制器 - 实名认证相关功能
提供实名认证、身份验证等造数功能
"""
from fastapi import APIRouter, Query
import logging
from typing import Optional

from mcpService.domains.identity_service import (
    create_identity_verification, query_verification_status,
    upload_identity_materials, verify_bank_card, verify_mobile
)

# 配置日志
logger = logging.getLogger(__name__)

# 创建实名域路由器
identity_router = APIRouter(
    prefix="/identity",
    tags=["实名域"],
    responses={404: {"description": "Not found"}}
)


@identity_router.post(
    "/create_identity_verification",
    summary="🆔 创建实名认证",
    description="创建实名认证流程",
    operation_id="identity_create_verification"
)
async def create_identity_verification_endpoint(
    name: str,
    idcard: str,
    mobile: str,
    verification_type: str = "BASIC",
    environment: Optional[str] = Query(None, description="环境类型")
):
    """创建实名认证"""
    return create_identity_verification(
        name, idcard, mobile, verification_type, environment
    )


@identity_router.post(
    "/query_verification_status",
    summary="📊 查询认证状态",
    description="查询实名认证状态",
    operation_id="identity_query_verification_status"
)
async def query_verification_status_endpoint(
    verification_id: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """查询认证状态"""
    return query_verification_status(verification_id, environment)


@identity_router.post(
    "/upload_identity_materials",
    summary="📄 上传身份材料",
    description="上传身份认证材料",
    operation_id="identity_upload_materials"
)
async def upload_identity_materials_endpoint(
    verification_id: str,
    material_type: str,
    file_url: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """上传身份材料"""
    return upload_identity_materials(
        verification_id, material_type, file_url, environment
    )


@identity_router.post(
    "/verify_bank_card",
    summary="💳 银行卡验证",
    description="验证银行卡信息",
    operation_id="identity_verify_bank_card"
)
async def verify_bank_card_endpoint(
    name: str,
    idcard: str,
    bank_card: str,
    mobile: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """银行卡验证"""
    return verify_bank_card(name, idcard, bank_card, mobile, environment)


@identity_router.post(
    "/verify_mobile",
    summary="📱 手机号验证",
    description="验证手机号信息",
    operation_id="identity_verify_mobile"
)
async def verify_mobile_endpoint(
    name: str,
    idcard: str,
    mobile: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """手机号验证"""
    return verify_mobile(name, idcard, mobile, environment)
