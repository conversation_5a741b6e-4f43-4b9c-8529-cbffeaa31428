#!/usr/bin/env python3
"""
基础服务类 - 服务层基类
提供通用的服务功能和工具方法
"""
import logging
from typing import Dict, Any, Optional
from .http_client import http_client
from .environment_manager import env_manager
from .response_formatter import formatter

logger = logging.getLogger(__name__)


class BaseService:
    """基础服务类"""
    
    def __init__(self, domain: str):
        """
        初始化基础服务
        
        Args:
            domain: 业务域名称
        """
        self.domain = domain
        self.http_client = http_client
        self.env_manager = env_manager
        self.formatter = formatter
    
    def get_domain_config(self, environment: Optional[str] = None) -> Dict[str, Any]:
        """
        获取业务域配置
        
        Args:
            environment: 环境描述
            
        Returns:
            业务域配置
        """
        return self.env_manager.get_domain_config(self.domain, environment)
    
    def get_api_url(self, service: str = "sdk", environment: Optional[str] = None) -> str:
        """
        获取API地址
        
        Args:
            service: 服务类型
            environment: 环境描述
            
        Returns:
            API地址
        """
        # 优先使用业务域配置的base_url
        domain_config = self.get_domain_config(environment)
        if domain_config and "base_url" in domain_config:
            return domain_config["base_url"]
        
        # 否则使用通用API地址
        return self.env_manager.get_api_url(service, environment)
    
    def get_app_id(self, environment: Optional[str] = None) -> str:
        """
        获取应用ID
        
        Args:
            environment: 环境描述
            
        Returns:
            应用ID
        """
        # 优先使用业务域配置的app_id
        domain_config = self.get_domain_config(environment)
        if domain_config and "app_id" in domain_config:
            return domain_config["app_id"]
        
        # 否则使用通用应用ID
        return self.env_manager.get_app_id("default", environment)
    
    def make_api_request(
        self,
        path: str,
        data: Optional[Dict[str, Any]] = None,
        method: str = "POST",
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        environment: Optional[str] = None,
        service: str = "sdk",
        operation: str = "API调用"
    ) -> Dict[str, Any]:
        """
        发送API请求
        
        Args:
            path: API路径
            data: 请求数据
            method: 请求方法
            headers: 请求头
            params: URL参数
            environment: 环境描述
            service: 服务类型
            operation: 操作名称
            
        Returns:
            API响应
        """
        try:
            # 获取API基础地址
            base_url = self.get_api_url(service, environment)
            if not base_url:
                return self.formatter.error(f"无法获取{service}服务的API地址")
            
            # 构建完整URL
            url = f"{base_url.rstrip('/')}/{path.lstrip('/')}"
            
            # 发送请求
            result = self.http_client.make_request(
                url=url,
                method=method,
                data=data,
                headers=headers,
                params=params,
                environment=environment,
                domain=self.domain
            )
            
            # 格式化响应
            return self.formatter.format_api_response(
                api_result=result,
                success_message=f"{operation}成功",
                error_message=f"{operation}失败"
            )
            
        except Exception as e:
            logger.error(f"{operation}异常: {str(e)}")
            return self.formatter.error(
                message=f"{operation}异常: {str(e)}",
                details={"domain": self.domain, "path": path}
            )
    
    def format_response(
        self,
        data: Any,
        operation: str,
        environment: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        格式化响应
        
        Args:
            data: 响应数据
            operation: 操作名称
            environment: 环境描述
            
        Returns:
            格式化后的响应
        """
        return self.formatter.format_mcp_response(data, operation, environment)
