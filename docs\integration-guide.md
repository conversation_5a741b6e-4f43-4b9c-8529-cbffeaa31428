# 接入指南

## 📋 目录

- [快速接入](#快速接入)
- [MCP工具使用](#mcp工具使用)
- [自动代码生成](#自动代码生成)
- [环境配置](#环境配置)
- [业务域接入](#业务域接入)
- [常见问题](#常见问题)

## 🚀 快速接入

### 1. 环境准备

**系统要求**：
- Python 3.8+
- 支持MCP协议的AI工具

**安装步骤**：
```bash
# 1. 克隆项目
git clone <repository-url>
cd esign-qa-mcp-service

# 2. 安装依赖
pip install -r requirements.txt

# 3. 启动服务
python main.py
```

**验证安装**：
```bash
# 检查服务状态
curl http://localhost:8000/health_check

# 查看API文档
open http://localhost:8000/docs

# 检查MCP端点
curl http://localhost:8000/mcp
```

### 2. AI工具配置

#### lingma配置示例

```json
{
  "mcp": {
    "servers": {
      "esign-qa-platform": {
        "command": "python",
        "args": ["-m", "mcp_client"],
        "env": {
          "MCP_SERVER_URL": "http://localhost:8000/mcp"
        }
      }
    }
  }
}
```

#### Claude Desktop配置示例

```json
{
  "mcpServers": {
    "esign-qa-platform": {
      "command": "node",
      "args": ["mcp-client.js"],
      "env": {
        "MCP_SERVER_URL": "http://localhost:8000/mcp"
      }
    }
  }
}
```

## 🔧 MCP工具使用

### 可用工具列表

#### 证书域工具
- `certificate_get_test_account`：获取测试账号
- `certificate_create`：创建数字证书
- `certificate_query_detail`：查询证书详情
- `certificate_revoke`：吊销证书
- `certificate_update`：更新证书

#### 签署域工具
- `signing_create_flow`：创建签署流程
- `signing_add_signer`：添加签署人
- `signing_start`：启动签署
- `signing_query_status`：查询签署状态
- `signing_cancel`：取消签署

#### SaaS域工具
- `saas_register_person_account`：注册个人账号
- `saas_register_company_account`：注册企业账号
- `saas_create_organization`：创建组织
- `saas_add_member_to_org`：添加组织成员
- `saas_query_org_info`：查询组织信息

#### 实名域工具
- `identity_create_verification`：创建实名认证
- `identity_query_verification_status`：查询认证状态
- `identity_upload_materials`：上传身份材料
- `identity_verify_bank_card`：银行卡验证
- `identity_verify_mobile`：手机号验证

#### 意愿域工具
- `intention_create_verification`：创建意愿验证
- `intention_query_status`：查询意愿状态
- `intention_confirm`：确认意愿
- `intention_cancel`：取消意愿
- `intention_get_record`：获取意愿记录

#### 平台功能工具
- `platform_get_environment_info`：获取环境信息
- `platform_switch_environment`：切换环境
- `platform_generate_mcp_code`：生成MCP代码
- `platform_get_system_status`：获取系统状态
- `platform_get_api_statistics`：获取API统计
- `platform_get_version_info`：获取版本信息

### 工具调用示例

#### 1. 获取测试账号

**AI工具调用**：
```
请帮我获取一个测试账号，使用模拟环境
```

**MCP调用**：
```json
{
  "tool": "certificate_get_test_account",
  "arguments": {
    "environment": "使用模拟环境"
  }
}
```

**响应示例**：
```json
{
  "status": "success",
  "data": {
    "phone": "***********",
    "idcard": "341321196102220130",
    "name": "测试东广政",
    "idcardType": "IDCARD"
  },
  "message": "获取测试账号成功",
  "environment": "mock"
}
```

#### 2. 创建数字证书

**AI工具调用**：
```
使用刚才的测试账号创建一个SM2算法的数字证书
```

**MCP调用**：
```json
{
  "tool": "certificate_create",
  "arguments": {
    "cert_name": "测试东广政",
    "phone": "***********",
    "idcard": "341321196102220130",
    "algorithm": "SM2",
    "cert_time": "ONEYEAR"
  }
}
```

#### 3. 环境切换

**AI工具调用**：
```
切换到测试环境
```

**MCP调用**：
```json
{
  "tool": "platform_switch_environment",
  "arguments": {
    "environment": "test"
  }
}
```

## 🤖 自动代码生成

### 触发方式

在与AI工具的对话中，使用关键词"接入mcp"触发自动代码生成：

```
接入mcp

curl --location --request POST 'http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId' \
--header 'X-Tsign-Open-App-Id: **********' \
--header 'X-Tsign-Service-Group: non-standard-v3' \
--header 'X-Tsign-Open-Auth-Mode: simple' \
--header 'Content-Type: application/json' \
--data-raw '{
    "thirdPartyUserId": "z111zzzzz11z223321zzz",
    "name": "测试这是不内耗"
}'
```

### 生成结果

系统会自动生成：

1. **服务层代码**：
```python
def createByThirdPartyUserId(
    thirdPartyUserId: str,
    name: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    createByThirdPartyUserId接口调用
    
    参数说明:
    - thirdPartyUserId: str类型参数
    - name: str类型参数
    - environment: 环境描述，支持自然语言
    
    返回:
        API调用结果
    """
    # ... 实现代码
```

2. **控制器代码**：
```python
@saas_router.post(
    "/createByThirdPartyUserId",
    summary="🔧 createByThirdPartyUserId",
    description="createByThirdPartyUserId接口调用",
    operation_id="saas_createByThirdPartyUserId"
)
async def createByThirdPartyUserId_endpoint(
    thirdPartyUserId: str,
    name: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """createByThirdPartyUserId接口调用"""
    return createByThirdPartyUserId(thirdPartyUserId, name, environment)
```

3. **集成指南**：详细的代码集成步骤

### 支持的格式

- **curl命令**：标准的curl命令格式
- **多行curl**：支持换行符的curl命令
- **API规范**：JSON格式的API描述
- **交互式输入**：通过问答形式生成代码

## 🌐 环境配置

### 环境类型

1. **测试环境 (test)**：
   - 连接真实的测试服务器
   - 用于功能测试和集成测试
   - 数据会持久化保存

2. **模拟环境 (mock)**：
   - 模拟的API响应
   - 用于开发和调试
   - 数据不会真实保存

### 环境切换方法

#### 1. 自然语言切换

在任何接口调用中，通过environment参数使用自然语言描述：

```json
{
  "environment": "使用模拟环境"
}
```

支持的关键词：
- **测试环境**：测试、test、开发、dev
- **模拟环境**：模拟、mock、仿真、虚拟、假

#### 2. 显式切换

调用环境切换接口：

```json
{
  "tool": "platform_switch_environment",
  "arguments": {
    "environment": "mock"
  }
}
```

#### 3. 全局配置

修改配置文件中的默认环境：

```python
# app/core/config.py
DEFAULT_ENVIRONMENT: str = "test"  # 或 "mock"
```

### 环境配置文件

#### 测试环境配置 (config/test_env.yml)

```yaml
environment: test
description: "测试环境配置"
api_urls:
  sdk: "http://sdk.testk8s.tsign.cn"
  cert: "http://cert-service.testk8s.tsign.cn"
  footstone: "http://in-test-openapi.tsign.cn"
app_ids:
  default: "7876611670"
  cert: "1111564052"
  signing: "**********"
```

#### 模拟环境配置 (config/mock_env.yml)

```yaml
environment: mock
description: "模拟环境配置"
api_urls:
  sdk: "http://mock-sdk.tsign.cn"
  cert: "http://mock-cert.tsign.cn"
  footstone: "http://mock-openapi.tsign.cn"
app_ids:
  default: "mock_app_id"
  cert: "mock_cert_id"
  signing: "mock_signing_id"
```

### 自定义环境

1. **创建配置文件**：
```yaml
# config/custom_env.yml
environment: custom
description: "自定义环境配置"
api_urls:
  sdk: "http://custom-sdk.example.com"
# ... 其他配置
```

2. **更新环境检测**：
```python
# 在 EnvironmentManager 中添加新环境检测逻辑
def detect_environment_from_text(self, text: str) -> str:
    custom_keywords = ["自定义", "custom"]
    for keyword in custom_keywords:
        if keyword in text.lower():
            return "custom"
    # ... 其他检测逻辑
```

## 🎯 业务域接入

### 新业务域接入流程

#### 1. 创建服务层

```python
# mcpService/domains/new_domain_service.py
from mcpService.common.base_service import BaseService

class NewDomainService(BaseService):
    def __init__(self):
        super().__init__(domain="new_domain")

# 全局服务实例
new_domain_service = NewDomainService()

def new_function(param1: str, environment: Optional[str] = None):
    """新功能实现"""
    return new_domain_service.make_api_request(
        path="/new/api",
        data={"param1": param1},
        environment=environment,
        operation="新功能"
    )
```

#### 2. 创建控制器层

```python
# app/mcpController/domains/new_domain_controller.py
from fastapi import APIRouter, Query
from mcpService.domains.new_domain_service import new_function

new_domain_router = APIRouter(
    prefix="/new_domain",
    tags=["新业务域"]
)

@new_domain_router.post("/new_function")
async def new_function_endpoint(
    param1: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    return new_function(param1, environment)
```

#### 3. 更新配置

```yaml
# config/test_env.yml 和 config/mock_env.yml
business_domains:
  new_domain:
    name: "新业务域"
    description: "新业务域功能"
    app_id: "new_app_id"
    base_url: "http://new-service.tsign.cn"
```

#### 4. 注册路由

```python
# main.py
from app.mcpController.domains.new_domain_controller import new_domain_router

app.include_router(new_domain_router, prefix=f"{settings.API_V1_STR}")
```

#### 5. 更新MCP暴露

```python
# main.py
mcp = FastApiMCP(
    app,
    include_tags=["签署域", "SaaS域", "实名域", "意愿域", "证书域", "平台功能", "新业务域"]
)
```

### 接入最佳实践

1. **命名规范**：
   - 服务文件：`{domain}_service.py`
   - 控制器文件：`{domain}_controller.py`
   - 路由前缀：`/{domain}`
   - MCP工具：`{domain}_{operation}`

2. **错误处理**：
   - 继承BaseService获得统一错误处理
   - 使用ResponseFormatter格式化响应
   - 记录详细的错误日志

3. **环境支持**：
   - 所有接口都应支持environment参数
   - 在业务域配置中定义专用配置
   - 测试不同环境的功能

4. **文档完善**：
   - 添加详细的接口文档
   - 提供使用示例
   - 更新API参考文档

## ❓ 常见问题

### Q1: 如何调试MCP工具调用？

**A**: 
1. 查看API文档：http://localhost:8000/docs
2. 检查日志输出：服务启动时会显示详细日志
3. 使用健康检查：`curl http://localhost:8000/health_check`
4. 查看MCP端点：`curl http://localhost:8000/mcp`

### Q2: 环境切换不生效怎么办？

**A**:
1. 检查环境配置文件是否存在
2. 验证关键词是否正确
3. 查看日志中的环境检测信息
4. 使用显式环境切换接口

### Q3: 自动代码生成失败怎么办？

**A**:
1. 确保包含"接入mcp"关键词
2. 检查curl命令格式是否正确
3. 查看错误日志获取详细信息
4. 尝试使用简化的curl命令

### Q4: 如何添加新的API接口？

**A**:
1. 使用自动代码生成功能
2. 手动按照业务域接入流程
3. 参考现有接口的实现
4. 测试接口功能和MCP暴露

### Q5: 性能监控数据在哪里查看？

**A**:
```json
{
  "tool": "platform_get_system_status"
}
```
或访问：http://localhost:8000/docs 查看监控相关接口

### Q6: 如何自定义响应格式？

**A**:
继承ResponseFormatter类并重写相关方法：
```python
class CustomFormatter(ResponseFormatter):
    @staticmethod
    def success(data, message="操作成功"):
        # 自定义成功响应格式
        pass
```

### Q7: 如何处理并发请求？

**A**:
平台基于FastAPI，天然支持异步处理：
- 使用async/await语法
- 配置合适的worker数量
- 监控系统资源使用情况

## 📞 技术支持

- **文档问题**：查看docs目录下的详细文档
- **功能问题**：通过Issue提交问题
- **集成问题**：参考integration-guide.md
- **性能问题**：查看monitoring相关接口

## 🔄 版本更新

定期检查版本更新：
```json
{
  "tool": "platform_get_version_info"
}
```

查看更新日志：
```json
{
  "tool": "platform_get_changelog"
}
```
