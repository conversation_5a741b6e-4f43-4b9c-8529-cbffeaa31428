# 开发指南

## 📋 目录

- [开发环境搭建](#开发环境搭建)
- [代码结构说明](#代码结构说明)
- [开发规范](#开发规范)
- [测试指南](#测试指南)
- [调试技巧](#调试技巧)
- [性能优化](#性能优化)

## 🛠️ 开发环境搭建

### 系统要求

- **Python**: 3.8+
- **操作系统**: Windows/Linux/macOS
- **IDE**: VS Code/PyCharm (推荐)
- **Git**: 版本控制

### 环境安装

```bash
# 1. 克隆项目
git clone <repository-url>
cd esign-qa-mcp-service

# 2. 创建虚拟环境
python -m venv venv

# 3. 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate

# 4. 安装依赖
pip install -r requirements.txt

# 5. 安装开发依赖
pip install -r requirements-dev.txt
```

### 开发工具配置

#### VS Code配置

创建 `.vscode/settings.json`:
```json
{
  "python.defaultInterpreterPath": "./venv/bin/python",
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": true,
  "python.formatting.provider": "black",
  "python.sortImports.args": ["--profile", "black"],
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  }
}
```

#### 预提交钩子

```bash
# 安装pre-commit
pip install pre-commit

# 安装钩子
pre-commit install
```

创建 `.pre-commit-config.yaml`:
```yaml
repos:
  - repo: https://github.com/psf/black
    rev: 22.3.0
    hooks:
      - id: black
  - repo: https://github.com/pycqa/isort
    rev: 5.10.1
    hooks:
      - id: isort
  - repo: https://github.com/pycqa/flake8
    rev: 4.0.1
    hooks:
      - id: flake8
```

## 🏗️ 代码结构说明

### 项目架构

```
esign-qa-mcp-service/
├── app/                          # 应用层
│   ├── core/                     # 核心配置
│   │   └── config.py            # 配置管理
│   ├── mcpController/           # 控制器层
│   │   └── domains/             # 业务域控制器
│   └── testCasePromptAndFiles/  # 提示词文件
├── mcpService/                  # 服务层
│   ├── common/                  # 公共模块
│   ├── domains/                 # 业务域服务
│   └── platform/                # 平台服务
├── config/                      # 配置文件
├── docs/                        # 文档
├── tests/                       # 测试文件
├── main.py                      # 启动入口
└── requirements.txt             # 依赖列表
```

### 核心模块说明

#### 1. 配置管理 (app/core/config.py)

```python
class Settings(BaseSettings):
    """应用配置类"""
    PROJECT_NAME: str = "esign-qa-mcp-platform"
    VERSION: str = "2.0.0"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.env_config = EnvironmentConfig()
```

**职责**:
- 环境配置管理
- 应用参数配置
- 多环境支持

#### 2. 基础服务类 (mcpService/common/base_service.py)

```python
class BaseService:
    """基础服务类"""
    def __init__(self, domain: str):
        self.domain = domain
        self.http_client = http_client
        self.env_manager = env_manager
```

**职责**:
- 提供统一的服务基类
- 封装HTTP请求
- 环境管理集成

#### 3. HTTP客户端 (mcpService/common/http_client.py)

```python
class HttpClient:
    """HTTP客户端"""
    def make_request(self, url, method="POST", data=None, ...):
        # 统一的HTTP请求处理
        pass
```

**职责**:
- 统一HTTP请求处理
- 请求日志记录
- 错误处理和重试

### 开发模式

#### 1. 业务域开发模式

每个业务域包含：
- **服务层**: 业务逻辑实现
- **控制器层**: API接口定义
- **配置**: 域特定配置

#### 2. 分层开发模式

- **控制器层**: 只处理HTTP请求/响应
- **服务层**: 实现业务逻辑
- **公共层**: 提供通用功能

## 📝 开发规范

### 代码风格

#### 1. 命名规范

```python
# 类名：大驼峰
class CertificateService:
    pass

# 函数名：小写+下划线
def get_test_account():
    pass

# 变量名：小写+下划线
user_name = "test"

# 常量：大写+下划线
DEFAULT_TIMEOUT = 30

# 私有方法：前缀下划线
def _internal_method():
    pass
```

#### 2. 文档字符串

```python
def create_certificate(
    cert_name: str,
    phone: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    创建数字证书
    
    Args:
        cert_name: 证书姓名
        phone: 手机号
        environment: 环境描述，支持自然语言
        
    Returns:
        Dict[str, Any]: 证书创建结果
        
    Raises:
        ValueError: 参数验证失败
        RequestException: API请求失败
        
    Example:
        >>> result = create_certificate("张三", "***********")
        >>> print(result["status"])
        success
    """
```

#### 3. 类型注解

```python
from typing import Dict, Any, Optional, List

def process_data(
    data: Dict[str, Any],
    options: Optional[List[str]] = None
) -> Dict[str, Any]:
    pass
```

### 错误处理规范

#### 1. 异常处理

```python
def api_call():
    try:
        result = make_request()
        return formatter.success(result)
    except RequestException as e:
        logger.error(f"API请求失败: {str(e)}")
        return formatter.error(f"API请求失败: {str(e)}")
    except Exception as e:
        logger.error(f"未知错误: {str(e)}")
        return formatter.error(f"未知错误: {str(e)}")
```

#### 2. 日志记录

```python
import logging

logger = logging.getLogger(__name__)

def business_function():
    logger.info("开始执行业务逻辑")
    logger.debug(f"参数: {params}")
    
    try:
        result = process()
        logger.info("业务逻辑执行成功")
        return result
    except Exception as e:
        logger.error(f"业务逻辑执行失败: {str(e)}", exc_info=True)
        raise
```

### API设计规范

#### 1. 接口命名

```python
# 控制器方法命名
@router.post("/get_test_account")  # 动词+名词
async def get_test_account_endpoint():
    pass

# MCP工具命名
# 格式: {domain}_{operation}
# 示例: certificate_get_test_account
```

#### 2. 参数设计

```python
# 统一的参数设计
async def api_endpoint(
    # 业务参数
    param1: str,
    param2: int = 0,
    # 通用参数
    environment: Optional[str] = Query(None, description="环境类型")
):
    pass
```

#### 3. 响应格式

```python
# 使用统一的响应格式化器
return formatter.success(
    data=result_data,
    message="操作成功",
    extra_info={"operation": "create_certificate"}
)
```

## 🧪 测试指南

### 测试结构

```
tests/
├── unit/                    # 单元测试
│   ├── test_services/      # 服务层测试
│   ├── test_controllers/   # 控制器测试
│   └── test_common/        # 公共模块测试
├── integration/            # 集成测试
│   ├── test_api/          # API集成测试
│   └── test_mcp/          # MCP集成测试
├── fixtures/              # 测试数据
└── conftest.py           # 测试配置
```

### 单元测试示例

```python
# tests/unit/test_services/test_certificate_service.py
import pytest
from unittest.mock import Mock, patch
from mcpService.domains.certificate_service import get_test_account

class TestCertificateService:
    
    @patch('mcpService.domains.certificate_service.certificate_service')
    def test_get_test_account_success(self, mock_service):
        # 准备测试数据
        mock_response = {
            "status": "success",
            "data": {
                "name": "测试用户",
                "phone": "***********"
            }
        }
        mock_service.make_api_request.return_value = mock_response
        
        # 执行测试
        result = get_test_account()
        
        # 验证结果
        assert result["status"] == "success"
        assert "name" in result["data"]
        mock_service.make_api_request.assert_called_once()
    
    def test_get_test_account_with_environment(self):
        # 测试环境参数
        result = get_test_account(environment="模拟环境")
        # 验证环境检测逻辑
        pass
```

### 集成测试示例

```python
# tests/integration/test_api/test_certificate_api.py
import pytest
from fastapi.testclient import TestClient
from main import create_app

@pytest.fixture
def client():
    app = create_app()
    return TestClient(app)

class TestCertificateAPI:
    
    def test_get_test_account_endpoint(self, client):
        response = client.post("/api/v1/certificate/get_test_account")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "data" in data
    
    def test_create_certificate_endpoint(self, client):
        payload = {
            "cert_name": "测试用户",
            "phone": "***********",
            "idcard": "123456789012345678"
        }
        
        response = client.post(
            "/api/v1/certificate/create_certificate",
            json=payload
        )
        
        assert response.status_code == 200
```

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/unit/test_services/test_certificate_service.py

# 运行特定测试方法
pytest tests/unit/test_services/test_certificate_service.py::TestCertificateService::test_get_test_account_success

# 生成覆盖率报告
pytest --cov=mcpService --cov-report=html

# 运行性能测试
pytest tests/performance/ -v
```

## 🐛 调试技巧

### 1. 日志调试

```python
# 设置详细日志级别
import logging
logging.basicConfig(level=logging.DEBUG)

# 在代码中添加调试日志
logger.debug(f"请求参数: {params}")
logger.debug(f"API响应: {response}")
```

### 2. 断点调试

```python
# 使用pdb调试
import pdb; pdb.set_trace()

# 使用ipdb调试（推荐）
import ipdb; ipdb.set_trace()
```

### 3. API调试

```bash
# 使用curl测试API
curl -X POST http://localhost:8000/api/v1/certificate/get_test_account \
  -H "Content-Type: application/json" \
  -d '{}'

# 使用httpie测试API
http POST localhost:8000/api/v1/certificate/get_test_account
```

### 4. MCP调试

```python
# 检查MCP工具注册
curl http://localhost:8000/mcp

# 测试MCP工具调用
curl -X POST http://localhost:8000/mcp/tools/call \
  -H "Content-Type: application/json" \
  -d '{
    "name": "certificate_get_test_account",
    "arguments": {}
  }'
```

### 5. 环境调试

```python
# 检查环境配置
from app.core.config import settings
print(settings.current_config)

# 测试环境切换
from mcpService.common.environment_manager import env_manager
result = env_manager.detect_environment("使用模拟环境")
print(f"检测到环境: {result}")
```

## ⚡ 性能优化

### 1. 异步编程

```python
# 使用异步函数
async def async_api_call():
    async with httpx.AsyncClient() as client:
        response = await client.post(url, json=data)
        return response.json()

# 并发处理
import asyncio

async def batch_process(items):
    tasks = [process_item(item) for item in items]
    results = await asyncio.gather(*tasks)
    return results
```

### 2. 缓存优化

```python
from functools import lru_cache

@lru_cache(maxsize=128)
def get_config(env: str):
    # 缓存配置信息
    return load_config(env)

# 使用Redis缓存
import redis
cache = redis.Redis()

def cached_api_call(key):
    result = cache.get(key)
    if result:
        return json.loads(result)
    
    result = api_call()
    cache.setex(key, 300, json.dumps(result))  # 5分钟缓存
    return result
```

### 3. 数据库优化

```python
# 使用连接池
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool

engine = create_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=10,
    max_overflow=20
)
```

### 4. 监控和分析

```python
# 添加性能监控
import time
from functools import wraps

def monitor_performance(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        logger.info(f"{func.__name__} 执行时间: {end_time - start_time:.3f}s")
        return result
    return wrapper

@monitor_performance
def slow_function():
    # 需要监控的函数
    pass
```

## 🔧 开发工具

### 1. 代码质量工具

```bash
# 代码格式化
black .

# 导入排序
isort .

# 代码检查
flake8 .
pylint mcpService/

# 类型检查
mypy mcpService/
```

### 2. 依赖管理

```bash
# 生成requirements.txt
pip freeze > requirements.txt

# 检查依赖安全性
safety check

# 检查依赖更新
pip-check
```

### 3. 文档生成

```bash
# 生成API文档
python -c "
import uvicorn
from main import create_app
app = create_app()
# 访问 http://localhost:8000/docs
uvicorn.run(app, host='0.0.0.0', port=8000)
"
```

## 📚 最佳实践

### 1. 代码组织

- **单一职责**: 每个模块只负责一个功能
- **依赖注入**: 通过参数传递依赖
- **配置外部化**: 配置信息放在配置文件中
- **错误处理**: 统一的错误处理机制

### 2. 性能考虑

- **异步优先**: 使用异步编程提高并发性能
- **缓存策略**: 合理使用缓存减少重复计算
- **连接复用**: 使用连接池管理数据库连接
- **监控指标**: 添加性能监控和指标收集

### 3. 安全实践

- **输入验证**: 验证所有输入参数
- **错误信息**: 不暴露敏感信息
- **日志安全**: 避免记录敏感数据
- **依赖更新**: 定期更新依赖包

### 4. 测试策略

- **测试驱动**: 先写测试再写代码
- **覆盖率**: 保持高测试覆盖率
- **集成测试**: 测试组件间的集成
- **性能测试**: 定期进行性能测试

## 🤝 贡献指南

### 1. 开发流程

1. Fork项目
2. 创建特性分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 创建Pull Request

### 2. 代码审查

- 代码风格符合规范
- 包含适当的测试
- 文档更新完整
- 性能影响评估

### 3. 发布流程

1. 更新版本号
2. 更新CHANGELOG.md
3. 运行完整测试套件
4. 创建发布标签
5. 部署到生产环境
