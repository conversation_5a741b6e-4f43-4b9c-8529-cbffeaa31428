#!/usr/bin/env python3
"""
平台控制器 - 造数平台通用功能
提供环境管理、代码生成、提示词管理等平台级功能
"""
from fastapi import APIRouter, Body, Query, File, UploadFile
import logging
from typing import Optional, Dict, Any, List

from mcpService.platform.prompt_service import (
    get_prompt_types, get_prompt_content, search_prompts,
    get_接口生成提示词, get_必填参数提示词, get_枚举值提示词,
    get_必填与枚举合并提示词, get_通用HttpRunner测试用例生成提示词,
    get_全套集测提示词
)
from mcpService.platform.code_generator import generate_mcp_code_from_curl
from mcpService.platform.environment_service import get_environment_info, switch_environment
from mcpService.platform.monitoring_service import (
    get_system_status, get_api_statistics, get_health_check, get_performance_metrics
)
from mcpService.platform.version_service import (
    get_version_info, get_changelog, check_compatibility, get_api_versions
)

# 配置日志
logger = logging.getLogger(__name__)

# 创建平台路由器
platform_router = APIRouter(
    prefix="/platform",
    tags=["平台功能"],
    responses={404: {"description": "Not found"}}
)


# ============= 环境管理 =============

@platform_router.post(
    "/get_environment_info",
    summary="🌐 获取环境信息",
    description="获取当前环境配置信息",
    operation_id="platform_get_environment_info"
)
async def get_environment_info_endpoint(
    environment: Optional[str] = Query(None, description="环境类型，支持自然语言描述")
):
    """获取环境配置信息"""
    return get_environment_info(environment)


@platform_router.post(
    "/switch_environment",
    summary="🔄 切换环境",
    description="切换当前使用的环境",
    operation_id="platform_switch_environment"
)
async def switch_environment_endpoint(
    environment: str = Body(..., description="环境类型，支持test或mock")
):
    """切换当前使用的环境"""
    return switch_environment(environment)


# ============= 代码生成 =============

@platform_router.post(
    "/generate_mcp_code",
    summary="🧩 生成MCP代码",
    description="根据curl命令或API描述生成MCP代码",
    operation_id="platform_generate_mcp_code"
)
async def generate_mcp_code_endpoint(
    curl_command: str = Body(..., description="curl命令或API描述"),
    domain: Optional[str] = Body(None, description="业务域，如signing、saas等"),
    method_name: Optional[str] = Body(None, description="方法名称，不提供则自动生成")
):
    """根据curl命令或API描述生成MCP代码"""
    return generate_mcp_code_from_curl(curl_command, domain, method_name)


# ============= 提示词管理 =============

@platform_router.post(
    "/get_prompt_types",
    summary="📋 获取提示词类型",
    description="获取所有可用的提示词类型",
    operation_id="platform_get_prompt_types"
)
async def get_prompt_types_endpoint():
    """获取所有可用的提示词类型"""
    return get_prompt_types()


@platform_router.post(
    "/get_prompt_content",
    summary="📄 获取提示词内容",
    description="获取指定类型的提示词内容",
    operation_id="platform_get_prompt_content"
)
async def get_prompt_content_endpoint(prompt_type: str):
    """获取指定类型的提示词内容"""
    return get_prompt_content(prompt_type)


@platform_router.post(
    "/search_prompts",
    summary="🔍 搜索提示词",
    description="根据关键词搜索提示词",
    operation_id="platform_search_prompts"
)
async def search_prompts_endpoint(keyword: str):
    """根据关键词搜索提示词"""
    return search_prompts(keyword)


@platform_router.post(
    "/get_interface_generation_prompt",
    summary="🔧 获取接口生成提示词",
    description="获取接口生成提示词",
    operation_id="platform_get_interface_generation_prompt"
)
async def get_interface_generation_prompt_endpoint():
    """获取接口生成提示词"""
    return get_接口生成提示词()


@platform_router.post(
    "/get_required_params_prompt",
    summary="📝 获取必填参数提示词",
    description="获取必填参数提示词",
    operation_id="platform_get_required_params_prompt"
)
async def get_required_params_prompt_endpoint():
    """获取必填参数提示词"""
    return get_必填参数提示词()


@platform_router.post(
    "/get_enum_values_prompt",
    summary="📊 获取枚举值提示词",
    description="获取枚举值提示词",
    operation_id="platform_get_enum_values_prompt"
)
async def get_enum_values_prompt_endpoint():
    """获取枚举值提示词"""
    return get_枚举值提示词()


@platform_router.post(
    "/get_required_enum_merged_prompt",
    summary="📋 获取必填与枚举合并提示词",
    description="获取必填与枚举合并提示词",
    operation_id="platform_get_required_enum_merged_prompt"
)
async def get_required_enum_merged_prompt_endpoint():
    """获取必填与枚举合并提示词"""
    return get_必填与枚举合并提示词()


@platform_router.post(
    "/get_general_httprunner_prompt",
    summary="🧪 获取通用HttpRunner测试用例生成提示词",
    description="获取通用HttpRunner测试用例生成提示词",
    operation_id="platform_get_general_httprunner_prompt"
)
async def get_general_httprunner_prompt_endpoint():
    """获取通用HttpRunner测试用例生成提示词"""
    return get_通用HttpRunner测试用例生成提示词()


@platform_router.post(
    "/get_all_test_prompts",
    summary="📚 获取全套集测提示词",
    description="获取所有集测提示词内容",
    operation_id="platform_get_all_test_prompts"
)
async def get_all_test_prompts_endpoint():
    """获取所有集测提示词内容"""
    return get_全套集测提示词()


# ============= 监控管理 =============

@platform_router.post(
    "/get_system_status",
    summary="🖥️ 获取系统状态",
    description="获取系统CPU、内存、磁盘等状态信息",
    operation_id="platform_get_system_status"
)
async def get_system_status_endpoint():
    """获取系统状态"""
    return get_system_status()


@platform_router.post(
    "/get_api_statistics",
    summary="📊 获取API统计",
    description="获取API调用统计信息",
    operation_id="platform_get_api_statistics"
)
async def get_api_statistics_endpoint():
    """获取API统计"""
    return get_api_statistics()


@platform_router.post(
    "/get_health_check",
    summary="❤️ 健康检查",
    description="获取系统健康状态",
    operation_id="platform_get_health_check"
)
async def get_health_check_endpoint():
    """健康检查"""
    return get_health_check()


@platform_router.post(
    "/get_performance_metrics",
    summary="⚡ 获取性能指标",
    description="获取API性能指标和响应时间统计",
    operation_id="platform_get_performance_metrics"
)
async def get_performance_metrics_endpoint():
    """获取性能指标"""
    return get_performance_metrics()


# ============= 版本管理 =============

@platform_router.post(
    "/get_version_info",
    summary="🏷️ 获取版本信息",
    description="获取平台版本信息",
    operation_id="platform_get_version_info"
)
async def get_version_info_endpoint():
    """获取版本信息"""
    return get_version_info()


@platform_router.post(
    "/get_changelog",
    summary="📋 获取更新日志",
    description="获取平台更新日志",
    operation_id="platform_get_changelog"
)
async def get_changelog_endpoint():
    """获取更新日志"""
    return get_changelog()


@platform_router.post(
    "/check_compatibility",
    summary="🔍 检查版本兼容性",
    description="检查客户端版本兼容性",
    operation_id="platform_check_compatibility"
)
async def check_compatibility_endpoint(
    client_version: str = Body(..., description="客户端版本号")
):
    """检查版本兼容性"""
    return check_compatibility(client_version)


@platform_router.post(
    "/get_api_versions",
    summary="🔗 获取API版本信息",
    description="获取支持的API版本信息",
    operation_id="platform_get_api_versions"
)
async def get_api_versions_endpoint():
    """获取API版本信息"""
    return get_api_versions()
