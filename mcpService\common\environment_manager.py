#!/usr/bin/env python3
"""
环境管理器 - 统一环境配置管理
支持自然语言环境切换和配置获取
"""
import logging
from typing import Dict, Any, Optional
from app.core.config import settings, env_manager as global_env_manager

logger = logging.getLogger(__name__)


class EnvironmentManager:
    """环境管理器"""

    def __init__(self):
        self.current_env = settings.DEFAULT_ENVIRONMENT

    def detect_environment(self, text: Optional[str] = None) -> str:
        """
        从文本中检测环境类型

        Args:
            text: 输入文本，支持自然语言描述

        Returns:
            环境类型：test 或 mock
        """
        if not text:
            return self.current_env

        detected_env = global_env_manager.detect_environment_from_text(text)
        self.current_env = detected_env
        return detected_env

    def get_environment_config(self, env: Optional[str] = None) -> Dict[str, Any]:
        """
        获取环境配置

        Args:
            env: 环境类型，不传则使用当前环境

        Returns:
            环境配置字典
        """
        current_env = env or self.current_env
        return settings.get_api_config(current_env)
    
    def get_api_url(self, service: str, env: Optional[str] = None) -> str:
        """
        获取API服务地址
        
        Args:
            service: 服务名称，如 sdk、cert、footstone
            env: 环境类型
            
        Returns:
            API服务地址
        """
        config = self.get_environment_config(env)
        api_urls = config.get("api_urls", {})
        return api_urls.get(service, "")
    
    def get_app_id(self, app_type: str = "default", env: Optional[str] = None) -> str:
        """
        获取应用ID
        
        Args:
            app_type: 应用类型，如 default、cert、signing
            env: 环境类型
            
        Returns:
            应用ID
        """
        config = self.get_environment_config(env)
        app_ids = config.get("app_ids", {})
        return app_ids.get(app_type, "")
    
    def get_headers(self, env: Optional[str] = None) -> Dict[str, str]:
        """
        获取请求头配置
        
        Args:
            env: 环境类型
            
        Returns:
            请求头字典
        """
        config = self.get_environment_config(env)
        headers = config.get("headers", {})
        
        # 添加应用ID到请求头
        app_id = self.get_app_id("default", env)
        if app_id:
            headers["X-Tsign-Open-App-Id"] = app_id
            
        return headers
    
    def get_domain_config(self, domain: str, env: Optional[str] = None) -> Dict[str, Any]:
        """
        获取业务域配置
        
        Args:
            domain: 业务域名称，如 signing、saas、identity、intention、certificate
            env: 环境类型
            
        Returns:
            业务域配置字典
        """
        config = self.get_environment_config(env)
        business_domains = config.get("business_domains", {})
        return business_domains.get(domain, {})
    
    def switch_environment(self, env: str) -> Dict[str, Any]:
        """
        切换环境
        
        Args:
            env: 目标环境类型
            
        Returns:
            切换结果
        """
        try:
            if env not in ["test", "mock"]:
                return {
                    "status": "error",
                    "message": f"不支持的环境类型: {env}，仅支持 test 或 mock"
                }
            
            settings.set_environment(env)
            self.current_env = env
            
            return {
                "status": "success",
                "data": {
                    "previous_env": self.current_env,
                    "current_env": env,
                    "config": settings.current_config
                },
                "message": f"环境已切换到: {env}"
            }
            
        except Exception as e:
            logger.error(f"切换环境失败: {str(e)}")
            return {
                "status": "error",
                "message": f"切换环境失败: {str(e)}"
            }


# 全局环境管理器实例
env_manager = EnvironmentManager()
