#!/usr/bin/env python3
"""
意愿域控制器 - 签署意愿相关功能
提供意愿确认、意愿验证等造数功能
"""
from fastapi import APIRouter, Query
import logging
from typing import Optional

from mcpService.domains.intention_service import (
    create_intention_verification, query_intention_status,
    confirm_intention, cancel_intention, get_intention_record
)

# 配置日志
logger = logging.getLogger(__name__)

# 创建意愿域路由器
intention_router = APIRouter(
    prefix="/intention",
    tags=["意愿域"],
    responses={404: {"description": "Not found"}}
)


@intention_router.post(
    "/create_intention_verification",
    summary="✅ 创建意愿验证",
    description="创建签署意愿验证流程",
    operation_id="intention_create_verification"
)
async def create_intention_verification_endpoint(
    signer_name: str,
    signer_mobile: str,
    signer_idcard: str,
    document_title: str,
    verification_type: str = "SMS",
    environment: Optional[str] = Query(None, description="环境类型")
):
    """创建意愿验证"""
    return create_intention_verification(
        signer_name, signer_mobile, signer_idcard, 
        document_title, verification_type, environment
    )


@intention_router.post(
    "/query_intention_status",
    summary="📊 查询意愿状态",
    description="查询签署意愿验证状态",
    operation_id="intention_query_status"
)
async def query_intention_status_endpoint(
    intention_id: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """查询意愿状态"""
    return query_intention_status(intention_id, environment)


@intention_router.post(
    "/confirm_intention",
    summary="✔️ 确认意愿",
    description="确认签署意愿",
    operation_id="intention_confirm"
)
async def confirm_intention_endpoint(
    intention_id: str,
    verification_code: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """确认意愿"""
    return confirm_intention(intention_id, verification_code, environment)


@intention_router.post(
    "/cancel_intention",
    summary="❌ 取消意愿",
    description="取消签署意愿",
    operation_id="intention_cancel"
)
async def cancel_intention_endpoint(
    intention_id: str,
    reason: str = "测试取消",
    environment: Optional[str] = Query(None, description="环境类型")
):
    """取消意愿"""
    return cancel_intention(intention_id, reason, environment)


@intention_router.post(
    "/get_intention_record",
    summary="📋 获取意愿记录",
    description="获取签署意愿记录",
    operation_id="intention_get_record"
)
async def get_intention_record_endpoint(
    signer_idcard: str,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """获取意愿记录"""
    return get_intention_record(signer_idcard, start_date, end_date, environment)
