#!/usr/bin/env python3
"""
版本服务 - 版本管理相关功能
提供版本信息、更新记录、兼容性检查等功能
"""
import os
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from mcpService.common.response_formatter import formatter
from app.core.config import settings

logger = logging.getLogger(__name__)


class VersionService:
    """版本服务类"""
    
    def __init__(self):
        self.version_file = "version.json"
        self.changelog_file = "CHANGELOG.md"
        self._ensure_version_file()
    
    def _ensure_version_file(self):
        """确保版本文件存在"""
        if not os.path.exists(self.version_file):
            default_version = {
                "version": "2.0.0",
                "build_date": datetime.now().isoformat(),
                "git_commit": "unknown",
                "environment": "development",
                "features": [
                    "多业务域架构",
                    "环境配置管理",
                    "自动代码生成",
                    "统一响应格式",
                    "MCP协议支持"
                ],
                "api_version": "v1",
                "mcp_version": "1.0",
                "dependencies": {
                    "fastapi": ">=0.100.0",
                    "fastapi-mcp": ">=0.1.0",
                    "pydantic": ">=2.0.0",
                    "requests": ">=2.28.0",
                    "pyyaml": ">=6.0"
                }
            }
            
            with open(self.version_file, 'w', encoding='utf-8') as f:
                json.dump(default_version, f, indent=2, ensure_ascii=False)
    
    def get_version_info(self) -> Dict[str, Any]:
        """
        获取版本信息
        
        Returns:
            版本信息
        """
        try:
            # 读取版本文件
            with open(self.version_file, 'r', encoding='utf-8') as f:
                version_data = json.load(f)
            
            # 添加运行时信息
            runtime_info = {
                "current_time": datetime.now().isoformat(),
                "python_version": f"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}",
                "platform": os.name,
                "config": {
                    "project_name": settings.PROJECT_NAME,
                    "project_description": settings.PROJECT_DESCRIPTION,
                    "version": settings.VERSION,
                    "host": settings.HOST,
                    "port": settings.PORT,
                    "api_prefix": settings.API_V1_STR
                }
            }
            
            return formatter.success(
                data={
                    **version_data,
                    "runtime": runtime_info
                },
                message="版本信息获取成功"
            )
            
        except Exception as e:
            logger.error(f"获取版本信息失败: {str(e)}")
            return formatter.error(
                message=f"获取版本信息失败: {str(e)}"
            )
    
    def get_changelog(self) -> Dict[str, Any]:
        """
        获取更新日志
        
        Returns:
            更新日志
        """
        try:
            changelog_content = ""
            
            if os.path.exists(self.changelog_file):
                with open(self.changelog_file, 'r', encoding='utf-8') as f:
                    changelog_content = f.read()
            else:
                # 创建默认更新日志
                changelog_content = """# 更新日志

## [2.0.0] - 2025-01-10

### 新增
- 🏗️ 重构为多业务域架构（签署、SaaS、实名、意愿、证书）
- 🌐 支持环境配置管理（测试环境、模拟环境）
- 🤖 自动代码生成功能，支持curl命令解析
- 📊 统一响应格式和错误处理
- 🔧 平台化功能：监控、日志、版本管理
- 📝 提示词管理服务
- 🚀 基于FastAPI的MCP服务架构

### 改进
- ✨ 统一的HTTP客户端和环境管理
- 📈 API调用监控和性能统计
- 🛡️ 更好的错误处理和日志记录
- 🔄 支持自然语言环境切换

### 技术栈
- FastAPI + MCP协议
- 多业务域分层架构
- 统一配置管理
- 自动化代码生成

## [1.0.0] - 2024-12-01

### 初始版本
- 基础MCP服务功能
- 证书管理
- 测试账号生成
- 提示词管理
"""
                
                with open(self.changelog_file, 'w', encoding='utf-8') as f:
                    f.write(changelog_content)
            
            return formatter.success(
                data={
                    "changelog": changelog_content,
                    "file_path": self.changelog_file
                },
                message="更新日志获取成功"
            )
            
        except Exception as e:
            logger.error(f"获取更新日志失败: {str(e)}")
            return formatter.error(
                message=f"获取更新日志失败: {str(e)}"
            )
    
    def check_compatibility(self, client_version: str) -> Dict[str, Any]:
        """
        检查版本兼容性
        
        Args:
            client_version: 客户端版本
            
        Returns:
            兼容性检查结果
        """
        try:
            # 读取当前版本
            with open(self.version_file, 'r', encoding='utf-8') as f:
                version_data = json.load(f)
            
            current_version = version_data.get("version", "2.0.0")
            api_version = version_data.get("api_version", "v1")
            mcp_version = version_data.get("mcp_version", "1.0")
            
            # 简单的版本兼容性检查
            def parse_version(version_str):
                try:
                    return tuple(map(int, version_str.split('.')))
                except:
                    return (0, 0, 0)
            
            current_ver = parse_version(current_version)
            client_ver = parse_version(client_version)
            
            # 兼容性规则：主版本号相同则兼容
            is_compatible = current_ver[0] == client_ver[0]
            
            # 生成兼容性报告
            compatibility_info = {
                "is_compatible": is_compatible,
                "current_version": current_version,
                "client_version": client_version,
                "api_version": api_version,
                "mcp_version": mcp_version,
                "compatibility_level": "full" if is_compatible else "none"
            }
            
            if not is_compatible:
                compatibility_info["recommendations"] = [
                    f"请升级客户端到 {current_version} 或兼容版本",
                    "检查API文档了解版本差异",
                    "联系技术支持获取升级指导"
                ]
            
            return formatter.success(
                data=compatibility_info,
                message="版本兼容性检查完成"
            )
            
        except Exception as e:
            logger.error(f"版本兼容性检查失败: {str(e)}")
            return formatter.error(
                message=f"版本兼容性检查失败: {str(e)}"
            )
    
    def get_api_versions(self) -> Dict[str, Any]:
        """
        获取API版本信息
        
        Returns:
            API版本信息
        """
        try:
            api_versions = {
                "current": "v1",
                "supported": ["v1"],
                "deprecated": [],
                "endpoints": {
                    "v1": {
                        "base_path": "/api/v1",
                        "domains": [
                            "signing",
                            "saas", 
                            "identity",
                            "intention",
                            "certificate",
                            "platform"
                        ],
                        "features": [
                            "环境切换",
                            "自动代码生成",
                            "监控统计",
                            "提示词管理"
                        ]
                    }
                },
                "mcp": {
                    "version": "1.0",
                    "protocol": "MCP",
                    "transport": "HTTP",
                    "features": [
                        "工具调用",
                        "自动发现",
                        "参数验证"
                    ]
                }
            }
            
            return formatter.success(
                data=api_versions,
                message="API版本信息获取成功"
            )
            
        except Exception as e:
            logger.error(f"获取API版本信息失败: {str(e)}")
            return formatter.error(
                message=f"获取API版本信息失败: {str(e)}"
            )


# 全局版本服务实例
version_service = VersionService()


# 导出函数
def get_version_info() -> Dict[str, Any]:
    """获取版本信息"""
    return version_service.get_version_info()


def get_changelog() -> Dict[str, Any]:
    """获取更新日志"""
    return version_service.get_changelog()


def check_compatibility(client_version: str) -> Dict[str, Any]:
    """检查版本兼容性"""
    return version_service.check_compatibility(client_version)


def get_api_versions() -> Dict[str, Any]:
    """获取API版本信息"""
    return version_service.get_api_versions()
