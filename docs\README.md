# E-Sign QA MCP 造数平台

![Platform Banner](https://img.shields.io/badge/Platform-E--Sign%20QA%20MCP-blue?style=for-the-badge)
![Version](https://img.shields.io/badge/Version-2.0.0-green?style=for-the-badge)
![License](https://img.shields.io/badge/License-MIT-yellow?style=for-the-badge)

## 🌟 平台简介

E-Sign QA MCP 造数平台是专为电子签名自动化测试场景设计的多域造数平台，基于 FastAPI + MCP 协议构建，提供统一的测试数据生成、环境管理、代码自动生成等功能。

### 🎯 核心特性

- **🏗️ 多业务域架构**：签署、SaaS、实名、意愿、证书五大业务域
- **🌐 智能环境管理**：支持测试环境和模拟环境自动切换
- **🤖 自动代码生成**：通过"接入mcp"关键词触发，支持curl命令解析
- **📊 统一响应格式**：标准化的API响应和错误处理
- **🔧 平台化功能**：监控、日志、版本管理等企业级功能
- **📝 提示词管理**：集成测试用例生成提示词
- **🚀 MCP协议支持**：与AI工具无缝集成

## 🚀 快速开始

### 环境要求

- Python 3.8+
- FastAPI 0.100.0+
- 支持MCP协议的AI工具（如lingma）

### 安装部署

```bash
# 1. 克隆项目
git clone <repository-url>
cd esign-qa-mcp-service

# 2. 安装依赖
pip install -r requirements.txt

# 3. 启动服务
python main.py
```

### 访问地址

- **API文档**: http://localhost:8000/docs
- **MCP端点**: http://localhost:8000/mcp
- **健康检查**: http://localhost:8000/health_check

## 📂 项目结构

```
esign-qa-mcp-service/
├── app/                           # 应用主目录
│   ├── core/                      # 核心配置
│   │   └── config.py             # 环境配置管理
│   ├── mcpController/            # 控制器层
│   │   └── domains/              # 业务域控制器
│   │       ├── signing_controller.py      # 签署域
│   │       ├── saas_controller.py         # SaaS域
│   │       ├── identity_controller.py     # 实名域
│   │       ├── intention_controller.py    # 意愿域
│   │       ├── certificate_controller.py  # 证书域
│   │       └── platform_controller.py     # 平台功能
│   └── testCasePromptAndFiles/   # 提示词和模板
├── mcpService/                   # 服务层
│   ├── common/                   # 公共模块
│   │   ├── base_service.py       # 基础服务类
│   │   ├── http_client.py        # HTTP客户端
│   │   ├── environment_manager.py # 环境管理器
│   │   ├── response_formatter.py  # 响应格式化器
│   │   └── mcp_template.py       # MCP模板生成器
│   ├── domains/                  # 业务域服务
│   │   ├── signing_service.py    # 签署域服务
│   │   ├── saas_service.py       # SaaS域服务
│   │   ├── identity_service.py   # 实名域服务
│   │   ├── intention_service.py  # 意愿域服务
│   │   └── certificate_service.py # 证书域服务
│   └── platform/                 # 平台服务
│       ├── environment_service.py # 环境服务
│       ├── code_generator.py     # 代码生成器
│       ├── prompt_service.py     # 提示词服务
│       ├── monitoring_service.py # 监控服务
│       └── version_service.py    # 版本服务
├── config/                       # 配置文件
│   ├── test_env.yml             # 测试环境配置
│   └── mock_env.yml             # 模拟环境配置
├── docs/                        # 文档目录
├── main.py                      # 启动入口
└── requirements.txt             # 依赖列表
```

## 🎯 业务域介绍

### 1. 签署域 (Signing)
- **功能**：电子签名流程管理
- **接口前缀**：`/api/v1/signing`
- **主要功能**：创建签署流程、添加签署人、启动签署、查询状态

### 2. SaaS域 (SaaS)
- **功能**：账号和组织管理
- **接口前缀**：`/api/v1/saas`
- **主要功能**：注册个人/企业账号、创建组织、管理成员

### 3. 实名域 (Identity)
- **功能**：实名认证管理
- **接口前缀**：`/api/v1/identity`
- **主要功能**：创建实名认证、上传材料、银行卡验证

### 4. 意愿域 (Intention)
- **功能**：签署意愿管理
- **接口前缀**：`/api/v1/intention`
- **主要功能**：意愿验证、确认意愿、获取记录

### 5. 证书域 (Certificate)
- **功能**：数字证书管理
- **接口前缀**：`/api/v1/certificate`
- **主要功能**：创建证书、查询详情、吊销更新

### 6. 平台功能 (Platform)
- **功能**：平台级管理功能
- **接口前缀**：`/api/v1/platform`
- **主要功能**：环境管理、代码生成、监控统计

## 🌐 环境管理

### 支持的环境

- **测试环境 (test)**：连接真实的测试服务器
- **模拟环境 (mock)**：用于模拟测试场景

### 环境切换方式

1. **自然语言切换**：在接口调用时传入环境描述
   ```json
   {
     "environment": "使用模拟环境"
   }
   ```

2. **显式切换**：调用环境切换接口
   ```bash
   POST /api/v1/platform/switch_environment
   {
     "environment": "mock"
   }
   ```

### 环境配置文件

- `config/test_env.yml`：测试环境配置
- `config/mock_env.yml`：模拟环境配置

## 🤖 自动代码生成

### 触发方式

在输入中包含关键词"接入mcp"，然后提供curl命令：

```
接入mcp

curl --location --request POST 'http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId' \
--header 'X-Tsign-Open-App-Id: **********' \
--header 'X-Tsign-Service-Group: non-standard-v3' \
--header 'X-Tsign-Open-Auth-Mode: simple' \
--header 'Content-Type: application/json' \
--data-raw '{
    "thirdPartyUserId": "z111zzzzz11z223321zzz",
    "name": "测试这是不内耗"
}'
```

### 生成内容

- **服务层代码**：业务逻辑实现
- **控制器代码**：API接口定义
- **集成指南**：代码集成步骤
- **文件路径建议**：推荐的文件位置

## 📊 监控和统计

### 系统监控

- **系统状态**：CPU、内存、磁盘使用情况
- **API统计**：调用次数、成功率、响应时间
- **健康检查**：系统健康状态评估
- **性能指标**：响应时间百分位数统计

### 访问方式

```bash
# 获取系统状态
POST /api/v1/platform/get_system_status

# 获取API统计
POST /api/v1/platform/get_api_statistics

# 健康检查
POST /api/v1/platform/get_health_check

# 性能指标
POST /api/v1/platform/get_performance_metrics
```

## 📝 提示词管理

### 支持的提示词类型

- 接口生成提示词
- 必填参数提示词
- 枚举值提示词
- HttpRunner测试用例生成提示词
- 全套集测提示词

### 使用方式

```bash
# 获取提示词类型
POST /api/v1/platform/get_prompt_types

# 获取提示词内容
POST /api/v1/platform/get_prompt_content
{
  "prompt_type": "接口生成提示词"
}

# 搜索提示词
POST /api/v1/platform/search_prompts
{
  "keyword": "HttpRunner"
}
```

## 🔗 MCP集成

### 与AI工具集成

平台支持MCP协议，可以与支持MCP的AI工具（如lingma）无缝集成：

1. **工具自动发现**：AI工具可以自动发现平台提供的所有工具
2. **参数验证**：自动验证工具调用参数
3. **环境感知**：支持自然语言环境切换
4. **错误处理**：统一的错误响应格式

### MCP工具命名规范

- 格式：`{domain}_{operation}`
- 示例：
  - `certificate_get_test_account`：获取测试账号
  - `signing_create_flow`：创建签署流程
  - `platform_generate_mcp_code`：生成MCP代码

## 📚 更多文档

- [架构设计文档](./architecture.md)
- [接入指南](./integration-guide.md)
- [API参考文档](./api-reference.md)
- [开发指南](./development-guide.md)
- [部署指南](./deployment-guide.md)
- [故障排除](./troubleshooting.md)

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../LICENSE) 文件了解详情。

## 📞 联系我们

- 项目维护者：E-Sign QA Team
- 技术支持：通过 Issue 提交问题
- 文档反馈：欢迎提交文档改进建议
