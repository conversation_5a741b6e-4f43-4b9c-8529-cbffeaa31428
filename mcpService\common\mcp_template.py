#!/usr/bin/env python3
"""
MCP模板生成器 - 自动生成MCP代码模板
支持从curl命令、API描述等生成标准化的MCP代码
"""
import re
import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

logger = logging.getLogger(__name__)


class MCPTemplate:
    """MCP代码模板生成器"""
    
    def __init__(self):
        self.domain_mapping = {
            "signing": "签署域",
            "saas": "SaaS域", 
            "identity": "实名域",
            "intention": "意愿域",
            "certificate": "证书域"
        }
    
    def parse_curl_command(self, curl_command: str) -> Dict[str, Any]:
        """
        解析curl命令
        
        Args:
            curl_command: curl命令字符串
            
        Returns:
            解析结果
        """
        try:
            # 提取URL
            url_match = re.search(r"curl.*?['\"]([^'\"]+)['\"]", curl_command)
            if not url_match:
                url_match = re.search(r"curl.*?(\S+)", curl_command)
            
            url = url_match.group(1) if url_match else ""
            
            # 提取请求方法
            method_match = re.search(r"--request\s+(\w+)", curl_command)
            method = method_match.group(1) if method_match else "POST"
            
            # 提取请求头
            headers = {}
            header_matches = re.findall(r"--header\s+['\"]([^'\"]+)['\"]", curl_command)
            for header in header_matches:
                if ":" in header:
                    key, value = header.split(":", 1)
                    headers[key.strip()] = value.strip()
            
            # 提取请求体
            data = {}
            data_match = re.search(r"--data-raw\s+['\"]({.*?})['\"]", curl_command, re.DOTALL)
            if data_match:
                try:
                    data = json.loads(data_match.group(1))
                except json.JSONDecodeError:
                    data = {"raw": data_match.group(1)}
            
            # 解析URL路径和参数
            url_parts = url.split("?")
            base_url = url_parts[0]
            path = base_url.split("/")[-1] if "/" in base_url else base_url
            
            return {
                "url": url,
                "method": method,
                "headers": headers,
                "data": data,
                "path": path,
                "base_url": base_url
            }
            
        except Exception as e:
            logger.error(f"解析curl命令失败: {str(e)}")
            return {}
    
    def generate_method_name(self, path: str, operation: str = "") -> str:
        """
        生成方法名称
        
        Args:
            path: API路径
            operation: 操作描述
            
        Returns:
            方法名称
        """
        # 从路径提取关键词
        path_parts = path.replace("/", "_").replace("-", "_")
        path_parts = re.sub(r"[^a-zA-Z0-9_\u4e00-\u9fa5]", "", path_parts)
        
        # 如果有操作描述，优先使用
        if operation:
            operation_clean = re.sub(r"[^a-zA-Z0-9_\u4e00-\u9fa5]", "", operation)
            return operation_clean or path_parts or "api_call"
        
        return path_parts or "api_call"
    
    def generate_service_code(
        self,
        method_name: str,
        api_info: Dict[str, Any],
        domain: str = "common",
        description: str = ""
    ) -> str:
        """
        生成服务层代码
        
        Args:
            method_name: 方法名称
            api_info: API信息
            domain: 业务域
            description: 方法描述
            
        Returns:
            服务层代码
        """
        # 生成参数列表
        params = []
        param_docs = []
        
        # 从请求数据中提取参数
        data = api_info.get("data", {})
        for key, value in data.items():
            param_type = "str"
            if isinstance(value, int):
                param_type = "int"
            elif isinstance(value, bool):
                param_type = "bool"
            elif isinstance(value, list):
                param_type = "List[str]"
            
            params.append(f"{key}: {param_type}")
            param_docs.append(f"    - {key}: {type(value).__name__}类型参数")
        
        # 添加环境参数
        params.append("environment: Optional[str] = None")
        param_docs.append("    - environment: 环境描述，支持自然语言")
        
        params_str = ",\n    ".join(params)
        param_docs_str = "\n".join(param_docs)
        
        # 生成请求数据构建代码
        data_construction = []
        for key in data.keys():
            data_construction.append(f'        "{key}": {key}')
        
        data_construction_str = ",\n".join(data_construction)
        
        # 生成代码模板
        template = f'''def {method_name}(
    {params_str}
) -> Dict[str, Any]:
    """
    {description or f"{method_name}接口调用"}
    
    参数说明:
{param_docs_str}
    
    返回:
        API调用结果
    """
    try:
        # 构建请求数据
        request_data = {{
{data_construction_str}
        }}
        
        # 发送API请求
        result = self.make_api_request(
            path="{api_info.get('path', '')}",
            data=request_data,
            method="{api_info.get('method', 'POST')}",
            environment=environment,
            operation="{description or method_name}"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"{method_name}异常: {{str(e)}}")
        return self.formatter.error(
            message=f"{method_name}异常: {{str(e)}}",
            details={{"method": "{method_name}", "domain": "{domain}"}}
        )'''
        
        return template
    
    def generate_controller_code(
        self,
        method_name: str,
        api_info: Dict[str, Any],
        domain: str = "common",
        description: str = ""
    ) -> str:
        """
        生成控制器代码
        
        Args:
            method_name: 方法名称
            api_info: API信息
            domain: 业务域
            description: 方法描述
            
        Returns:
            控制器代码
        """
        # 生成参数列表
        params = []
        
        # 从请求数据中提取参数
        data = api_info.get("data", {})
        for key, value in data.items():
            param_type = "str"
            if isinstance(value, int):
                param_type = "int"
            elif isinstance(value, bool):
                param_type = "bool"
            elif isinstance(value, list):
                param_type = "List[str]"
            
            params.append(f"{key}: {param_type}")
        
        # 添加环境参数
        params.append('environment: Optional[str] = Query(None, description="环境类型")')
        
        params_str = ",\n    ".join(params)
        
        # 生成调用参数
        call_params = [key for key in data.keys()]
        call_params.append("environment")
        call_params_str = ", ".join(call_params)
        
        # 生成路由路径
        route_path = f"/{method_name}"
        
        # 生成代码模板
        template = f'''@{domain}_router.post(
    "{route_path}",
    summary="🔧 {description or method_name}",
    description="{description or f'{method_name}接口调用'}",
    operation_id="{domain}_{method_name}"
)
async def {method_name}_endpoint(
    {params_str}
):
    """
    {description or f"{method_name}接口调用"}
    
    支持环境切换：
    - 默认使用测试环境
    - 如果描述中包含"模拟"、"mock"等关键词，则使用模拟环境
    """
    return {method_name}({call_params_str})'''
        
        return template
    
    def generate_complete_mcp_code(
        self,
        curl_command: str,
        domain: Optional[str] = None,
        method_name: Optional[str] = None,
        description: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        生成完整的MCP代码
        
        Args:
            curl_command: curl命令
            domain: 业务域
            method_name: 方法名称
            description: 描述
            
        Returns:
            生成的代码和相关信息
        """
        try:
            # 解析curl命令
            api_info = self.parse_curl_command(curl_command)
            if not api_info:
                return {
                    "status": "error",
                    "message": "无法解析curl命令"
                }
            
            # 自动检测业务域
            if not domain:
                url = api_info.get("url", "")
                if "cert" in url.lower():
                    domain = "certificate"
                elif "sign" in url.lower():
                    domain = "signing"
                elif "account" in url.lower() or "user" in url.lower():
                    domain = "saas"
                elif "identity" in url.lower() or "real" in url.lower():
                    domain = "identity"
                else:
                    domain = "common"
            
            # 生成方法名
            if not method_name:
                method_name = self.generate_method_name(
                    api_info.get("path", ""), 
                    description or ""
                )
            
            # 生成描述
            if not description:
                path = api_info.get("path", "")
                description = f"{path}接口调用"
            
            # 生成服务层代码
            service_code = self.generate_service_code(
                method_name, api_info, domain, description
            )
            
            # 生成控制器代码
            controller_code = self.generate_controller_code(
                method_name, api_info, domain, description
            )
            
            # 生成文件路径建议
            service_file = f"mcpService/domains/{domain}_service.py"
            controller_file = f"app/mcpController/domains/{domain}_controller.py"
            
            return {
                "status": "success",
                "data": {
                    "method_name": method_name,
                    "domain": domain,
                    "description": description,
                    "api_info": api_info,
                    "service_code": service_code,
                    "controller_code": controller_code,
                    "files": {
                        "service_file": service_file,
                        "controller_file": controller_file
                    },
                    "integration_guide": self._generate_integration_guide(
                        method_name, domain, service_file, controller_file
                    )
                },
                "message": "MCP代码生成成功"
            }
            
        except Exception as e:
            logger.error(f"生成MCP代码失败: {str(e)}")
            return {
                "status": "error",
                "message": f"生成MCP代码失败: {str(e)}"
            }
    
    def _generate_integration_guide(
        self,
        method_name: str,
        domain: str,
        service_file: str,
        controller_file: str
    ) -> str:
        """生成集成指南"""
        return f"""
# MCP代码集成指南

## 1. 添加服务层代码
将生成的服务层代码添加到文件: `{service_file}`

## 2. 添加控制器代码  
将生成的控制器代码添加到文件: `{controller_file}`

## 3. 导入服务方法
在 `{controller_file}` 文件顶部添加导入:
```python
from mcpService.domains.{domain}_service import {method_name}
```

## 4. 更新__init__.py
在 `mcpService/domains/__init__.py` 中添加:
```python
from .{domain}_service import {method_name}
```

## 5. 测试接口
启动服务后，可以通过以下方式测试:
- API文档: http://localhost:8000/docs
- MCP调用: 通过MCP客户端调用 `{domain}_{method_name}` 工具

## 6. 环境切换
接口支持环境切换，在调用时可以传入environment参数:
- "测试环境" 或 "test" -> 使用测试环境
- "模拟环境" 或 "mock" -> 使用模拟环境
"""


# 全局MCP模板生成器实例
mcp_template = MCPTemplate()
