"""
应用配置设置 - 造数平台版本
支持多环境配置和自然语言环境切换
"""
from pydantic_settings import BaseSettings
from typing import Optional, Dict, Any
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()  # 默认加载 .env
if os.path.exists('.env_sml'):
    load_dotenv('.env_sml', override=True)  # 如果存在 .env_sml 则覆盖加载

class EnvironmentManager:
    """环境管理器 - 简化版本"""

    def __init__(self):
        self._current_env = "test"

    def detect_environment_from_text(self, text: str) -> str:
        """从自然语言文本中检测环境类型"""
        if not text:
            return self._current_env

        text_lower = text.lower()
        mock_keywords = ["模拟", "mock", "仿真", "虚拟", "假", "模拟环境"]

        for keyword in mock_keywords:
            if keyword in text_lower:
                return "mock"

        return "test"  # 默认测试环境

    def set_environment(self, env: str):
        """设置当前环境"""
        if env in ["test", "mock"]:
            self._current_env = env

    @property
    def current_environment(self) -> str:
        """获取当前环境"""
        return self._current_env

# 全局环境管理器
env_manager = EnvironmentManager()

class Settings(BaseSettings):
    """应用配置类"""

    # 项目基本信息
    PROJECT_NAME: str = "esign-qa-mcp-platform"
    PROJECT_DESCRIPTION: str = "电子签名QA造数平台MCP服务"
    VERSION: str = "2.0.0"

    # 服务配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    API_V1_STR: str = "/api/v1"

    # MCP配置
    MCP_NAME: str = "E-Sign QA Data Platform"
    MCP_DESCRIPTION: str = "专业的电子签名QA造数平台，支持多业务域和环境切换"

    # 数据库配置
    DATABASE_URL: str = "sqlite:///./platform.db"

    # 环境管理
    DEFAULT_ENVIRONMENT: str = "test"

    # API配置 - 测试环境
    TEST_SDK_URL: str = "http://sdk.testk8s.tsign.cn"
    TEST_CERT_URL: str = "http://cert-service.testk8s.tsign.cn"
    TEST_FOOTSTONE_URL: str = "http://in-test-openapi.tsign.cn"
    TEST_APP_ID: str = "7876611670"
    TEST_CERT_APP_ID: str = "1111564052"

    # API配置 - 模拟环境
    MOCK_SDK_URL: str = "http://mock-sdk.tsign.cn"
    MOCK_CERT_URL: str = "http://mock-cert.tsign.cn"
    MOCK_FOOTSTONE_URL: str = "http://mock-openapi.tsign.cn"
    MOCK_APP_ID: str = "mock_app_id"
    MOCK_CERT_APP_ID: str = "mock_cert_id"

    # 请求配置
    REQUEST_TIMEOUT: int = 30
    LOG_REQUESTS: bool = True

    # AI模型配置
    DEEPSEEK_API_KEY: str = "***********************************"
    DEEPSEEK_BASE_URL: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    DEEPSEEK_MODEL: str = "qwen-turbo"

    # 日志配置
    LOG_LEVEL: str = "INFO"

    # 平台功能配置
    ENABLE_AUTO_CODE_GENERATION: bool = True
    AUTO_CODE_TRIGGER_KEYWORD: str = "接入mcp"

    def get_api_config(self, env: str = None) -> Dict[str, Any]:
        """获取API配置"""
        current_env = env or env_manager.current_environment

        if current_env == "mock":
            return {
                "sdk_url": self.MOCK_SDK_URL,
                "cert_url": self.MOCK_CERT_URL,
                "footstone_url": self.MOCK_FOOTSTONE_URL,
                "app_id": self.MOCK_APP_ID,
                "cert_app_id": self.MOCK_CERT_APP_ID
            }
        else:
            return {
                "sdk_url": self.TEST_SDK_URL,
                "cert_url": self.TEST_CERT_URL,
                "footstone_url": self.TEST_FOOTSTONE_URL,
                "app_id": self.TEST_APP_ID,
                "cert_app_id": self.TEST_CERT_APP_ID
            }

    class Config:
        env_file = [".env", ".env_sml"]
        env_file_encoding = "utf-8"
        extra = "ignore"  # 忽略额外的环境变量

# 创建全局配置实例
settings = Settings()
