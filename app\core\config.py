"""
应用配置设置 - 造数平台版本
支持多环境配置和自然语言环境切换
"""
from pydantic_settings import BaseSettings
from typing import Optional, Dict, Any
import os
import yaml

class EnvironmentConfig:
    """环境配置管理类"""

    def __init__(self):
        self.config_dir = "config"
        self.test_config_file = os.path.join(self.config_dir, "test_env.yml")
        self.mock_config_file = os.path.join(self.config_dir, "mock_env.yml")
        self._ensure_config_files()

    def _ensure_config_files(self):
        """确保配置文件存在"""
        os.makedirs(self.config_dir, exist_ok=True)

        # 测试环境配置
        test_config = {
            "environment": "test",
            "description": "测试环境配置",
            "api_urls": {
                "sdk": "http://sdk.testk8s.tsign.cn",
                "cert": "http://cert-service.testk8s.tsign.cn",
                "footstone": "http://in-test-openapi.tsign.cn"
            },
            "app_ids": {
                "default": "7876611670",
                "cert": "1111564052"
            },
            "headers": {
                "X-Tsign-Open-Auth-Mode": "simple",
                "X-Tsign-Service-Group": "DEFAULT"
            }
        }

        # 模拟环境配置
        mock_config = {
            "environment": "mock",
            "description": "模拟环境配置",
            "api_urls": {
                "sdk": "http://mock-sdk.tsign.cn",
                "cert": "http://mock-cert.tsign.cn",
                "footstone": "http://mock-openapi.tsign.cn"
            },
            "app_ids": {
                "default": "mock_app_id",
                "cert": "mock_cert_id"
            },
            "headers": {
                "X-Tsign-Open-Auth-Mode": "mock",
                "X-Tsign-Service-Group": "MOCK"
            }
        }

        if not os.path.exists(self.test_config_file):
            with open(self.test_config_file, 'w', encoding='utf-8') as f:
                yaml.dump(test_config, f, default_flow_style=False, allow_unicode=True)

        if not os.path.exists(self.mock_config_file):
            with open(self.mock_config_file, 'w', encoding='utf-8') as f:
                yaml.dump(mock_config, f, default_flow_style=False, allow_unicode=True)

    def get_config(self, env: str = "test") -> Dict[str, Any]:
        """获取指定环境的配置"""
        config_file = self.test_config_file if env == "test" else self.mock_config_file

        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return {}

    def detect_environment_from_text(self, text: str) -> str:
        """从自然语言文本中检测环境类型"""
        text_lower = text.lower()
        mock_keywords = ["模拟", "mock", "仿真", "虚拟", "假", "模拟环境"]

        for keyword in mock_keywords:
            if keyword in text_lower:
                return "mock"

        return "test"  # 默认测试环境

class Settings(BaseSettings):
    """应用配置类"""

    # 项目基本信息
    PROJECT_NAME: str = "esign-qa-mcp-platform"
    PROJECT_DESCRIPTION: str = "电子签名QA造数平台MCP服务"
    VERSION: str = "2.0.0"

    # 服务配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    API_V1_STR: str = "/api/v1"

    # MCP配置
    MCP_NAME: str = "E-Sign QA Data Platform"
    MCP_DESCRIPTION: str = "专业的电子签名QA造数平台，支持多业务域和环境切换"

    # 数据库配置
    DATABASE_URL: str = "sqlite:///./platform.db"

    # 环境管理
    DEFAULT_ENVIRONMENT: str = "test"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.env_config = EnvironmentConfig()
        self._current_env = self.DEFAULT_ENVIRONMENT

    def set_environment(self, env: str):
        """设置当前环境"""
        if env in ["test", "mock"]:
            self._current_env = env

    def detect_and_set_environment(self, text: str):
        """从文本中检测并设置环境"""
        detected_env = self.env_config.detect_environment_from_text(text)
        self.set_environment(detected_env)
        return detected_env

    @property
    def current_environment(self) -> str:
        """获取当前环境"""
        return self._current_env

    @property
    def current_config(self) -> Dict[str, Any]:
        """获取当前环境配置"""
        return self.env_config.get_config(self._current_env)

    # 请求配置
    REQUEST_TIMEOUT: int = 30
    LOG_REQUESTS: bool = True

    # AI模型配置
    DEEPSEEK_API_KEY: str = "***********************************"
    DEEPSEEK_BASE_URL: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    DEEPSEEK_MODEL: str = "qwen-turbo"

    # 日志配置
    LOG_LEVEL: str = "INFO"

    # 平台功能配置
    ENABLE_AUTO_CODE_GENERATION: bool = True
    AUTO_CODE_TRIGGER_KEYWORD: str = "接入mcp"

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

# 创建全局配置实例
settings = Settings()
