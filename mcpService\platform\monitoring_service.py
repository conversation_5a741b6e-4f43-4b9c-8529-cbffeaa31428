#!/usr/bin/env python3
"""
监控服务 - 平台监控相关功能
提供系统状态监控、API调用统计、性能监控等功能
"""
import time
import psutil
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from collections import defaultdict, deque
from mcpService.common.response_formatter import formatter

logger = logging.getLogger(__name__)


class MonitoringService:
    """监控服务类"""
    
    def __init__(self):
        self.api_call_stats = defaultdict(int)
        self.api_response_times = defaultdict(deque)
        self.error_counts = defaultdict(int)
        self.start_time = datetime.now()
        self.max_response_time_records = 100  # 保留最近100次响应时间
    
    def record_api_call(self, endpoint: str, response_time: float, status: str):
        """
        记录API调用
        
        Args:
            endpoint: 接口端点
            response_time: 响应时间（秒）
            status: 状态（success/error）
        """
        self.api_call_stats[endpoint] += 1
        self.api_response_times[endpoint].append(response_time)
        
        # 保持队列大小
        if len(self.api_response_times[endpoint]) > self.max_response_time_records:
            self.api_response_times[endpoint].popleft()
        
        if status == "error":
            self.error_counts[endpoint] += 1
    
    def get_system_status(self) -> Dict[str, Any]:
        """
        获取系统状态
        
        Returns:
            系统状态信息
        """
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            
            # 运行时间
            uptime = datetime.now() - self.start_time
            
            return formatter.success(
                data={
                    "system": {
                        "cpu_percent": cpu_percent,
                        "memory": {
                            "total": memory.total,
                            "available": memory.available,
                            "percent": memory.percent,
                            "used": memory.used
                        },
                        "disk": {
                            "total": disk.total,
                            "used": disk.used,
                            "free": disk.free,
                            "percent": (disk.used / disk.total) * 100
                        },
                        "uptime": {
                            "seconds": uptime.total_seconds(),
                            "formatted": str(uptime)
                        }
                    },
                    "timestamp": datetime.now().isoformat()
                },
                message="系统状态获取成功"
            )
            
        except Exception as e:
            logger.error(f"获取系统状态失败: {str(e)}")
            return formatter.error(
                message=f"获取系统状态失败: {str(e)}"
            )
    
    def get_api_statistics(self) -> Dict[str, Any]:
        """
        获取API调用统计
        
        Returns:
            API统计信息
        """
        try:
            stats = {}
            
            for endpoint, count in self.api_call_stats.items():
                response_times = list(self.api_response_times[endpoint])
                error_count = self.error_counts[endpoint]
                
                if response_times:
                    avg_response_time = sum(response_times) / len(response_times)
                    max_response_time = max(response_times)
                    min_response_time = min(response_times)
                else:
                    avg_response_time = max_response_time = min_response_time = 0
                
                success_rate = ((count - error_count) / count * 100) if count > 0 else 0
                
                stats[endpoint] = {
                    "total_calls": count,
                    "error_count": error_count,
                    "success_rate": round(success_rate, 2),
                    "avg_response_time": round(avg_response_time, 3),
                    "max_response_time": round(max_response_time, 3),
                    "min_response_time": round(min_response_time, 3)
                }
            
            # 总体统计
            total_calls = sum(self.api_call_stats.values())
            total_errors = sum(self.error_counts.values())
            overall_success_rate = ((total_calls - total_errors) / total_calls * 100) if total_calls > 0 else 0
            
            return formatter.success(
                data={
                    "overall": {
                        "total_calls": total_calls,
                        "total_errors": total_errors,
                        "success_rate": round(overall_success_rate, 2),
                        "uptime": str(datetime.now() - self.start_time)
                    },
                    "endpoints": stats,
                    "timestamp": datetime.now().isoformat()
                },
                message="API统计信息获取成功"
            )
            
        except Exception as e:
            logger.error(f"获取API统计失败: {str(e)}")
            return formatter.error(
                message=f"获取API统计失败: {str(e)}"
            )
    
    def get_health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            健康状态
        """
        try:
            # 检查系统资源
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            
            # 健康状态判断
            health_status = "healthy"
            issues = []
            
            if cpu_percent > 80:
                health_status = "warning"
                issues.append(f"CPU使用率过高: {cpu_percent}%")
            
            if memory.percent > 80:
                health_status = "warning"
                issues.append(f"内存使用率过高: {memory.percent}%")
            
            if cpu_percent > 95 or memory.percent > 95:
                health_status = "critical"
            
            # 检查API错误率
            total_calls = sum(self.api_call_stats.values())
            total_errors = sum(self.error_counts.values())
            
            if total_calls > 0:
                error_rate = (total_errors / total_calls) * 100
                if error_rate > 10:
                    health_status = "warning"
                    issues.append(f"API错误率过高: {error_rate:.2f}%")
                if error_rate > 50:
                    health_status = "critical"
            
            return formatter.success(
                data={
                    "status": health_status,
                    "issues": issues,
                    "metrics": {
                        "cpu_percent": cpu_percent,
                        "memory_percent": memory.percent,
                        "api_error_rate": (total_errors / total_calls * 100) if total_calls > 0 else 0,
                        "uptime_seconds": (datetime.now() - self.start_time).total_seconds()
                    },
                    "timestamp": datetime.now().isoformat()
                },
                message=f"系统健康状态: {health_status}"
            )
            
        except Exception as e:
            logger.error(f"健康检查失败: {str(e)}")
            return formatter.error(
                message=f"健康检查失败: {str(e)}"
            )
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        获取性能指标
        
        Returns:
            性能指标
        """
        try:
            # 计算各端点的性能指标
            performance_data = {}
            
            for endpoint, response_times in self.api_response_times.items():
                if response_times:
                    times = list(response_times)
                    times.sort()
                    
                    # 计算百分位数
                    p50_idx = int(len(times) * 0.5)
                    p90_idx = int(len(times) * 0.9)
                    p95_idx = int(len(times) * 0.95)
                    p99_idx = int(len(times) * 0.99)
                    
                    performance_data[endpoint] = {
                        "p50": round(times[p50_idx] if p50_idx < len(times) else times[-1], 3),
                        "p90": round(times[p90_idx] if p90_idx < len(times) else times[-1], 3),
                        "p95": round(times[p95_idx] if p95_idx < len(times) else times[-1], 3),
                        "p99": round(times[p99_idx] if p99_idx < len(times) else times[-1], 3),
                        "avg": round(sum(times) / len(times), 3),
                        "max": round(max(times), 3),
                        "min": round(min(times), 3),
                        "sample_count": len(times)
                    }
            
            return formatter.success(
                data={
                    "performance_metrics": performance_data,
                    "timestamp": datetime.now().isoformat()
                },
                message="性能指标获取成功"
            )
            
        except Exception as e:
            logger.error(f"获取性能指标失败: {str(e)}")
            return formatter.error(
                message=f"获取性能指标失败: {str(e)}"
            )


# 全局监控服务实例
monitoring_service = MonitoringService()


# 导出函数
def get_system_status() -> Dict[str, Any]:
    """获取系统状态"""
    return monitoring_service.get_system_status()


def get_api_statistics() -> Dict[str, Any]:
    """获取API统计"""
    return monitoring_service.get_api_statistics()


def get_health_check() -> Dict[str, Any]:
    """健康检查"""
    return monitoring_service.get_health_check()


def get_performance_metrics() -> Dict[str, Any]:
    """获取性能指标"""
    return monitoring_service.get_performance_metrics()


def record_api_call(endpoint: str, response_time: float, status: str):
    """记录API调用"""
    monitoring_service.record_api_call(endpoint, response_time, status)
