#!/usr/bin/env python3
"""
证书域服务 - 数字证书相关功能实现
基于新的架构重构，使用统一的基础服务类
"""
import logging
from typing import Dict, Any, Optional
from mcpService.common.base_service import BaseService

logger = logging.getLogger(__name__)


class CertificateService(BaseService):
    """证书服务类"""
    
    def __init__(self):
        super().__init__(domain="certificate")


# 全局证书服务实例
certificate_service = CertificateService()


def get_test_account(environment: Optional[str] = None) -> Dict[str, Any]:
    """
    获取测试账号信息
    
    Args:
        environment: 环境描述，支持自然语言
        
    Returns:
        测试账号信息
    """
    try:
        result = certificate_service.make_api_request(
            path="/random/get",
            data={},
            method="GET",
            environment=environment,
            service="sdk",
            operation="获取测试账号"
        )
        
        # 处理响应数据格式
        if result.get("status") == "success" and "data" in result:
            api_data = result["data"]
            
            # 检查新的API响应格式
            if api_data.get("success") and api_data.get("accountList"):
                account = api_data["accountList"][0]
                formatted_data = {
                    "phone": account.get("phone"),
                    "idcard": account.get("idNo"),  # 注意字段名是 idNo
                    "name": account.get("name"),
                    "idcardType": "IDCARD",  # 默认值
                    "orgCode": account.get("orgCode"),
                    "englishName": account.get("englishName"),
                    "bankCard": account.get("bankCard"),
                    "orgName": account.get("orgName")
                }
                
                result["data"] = formatted_data
                result["message"] = "获取测试账号成功"
        
        return result
        
    except Exception as e:
        logger.error(f"获取测试账号异常: {str(e)}")
        return certificate_service.formatter.error(
            message=f"获取测试账号异常: {str(e)}",
            details={"domain": "certificate", "operation": "get_test_account"}
        )


def create_certificate(
    cert_name: str,
    phone: str,
    idcard: str,
    algorithm: str = "SM2",
    cert_time: str = "ONEYEAR",
    app_id: Optional[str] = None,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    创建数字证书
    
    Args:
        cert_name: 证书姓名
        phone: 手机号
        idcard: 身份证号
        algorithm: 加密算法，默认SM2
        cert_time: 证书有效期，默认一年
        app_id: 应用ID，不传则使用环境默认值
        environment: 环境描述，支持自然语言
        
    Returns:
        证书创建结果
    """
    try:
        # 构建请求数据
        request_data = {
            "certName": cert_name,
            "phone": phone,
            "idcard": idcard,
            "algorithm": algorithm,
            "certTime": cert_time
        }
        
        # 如果指定了app_id，添加到请求头
        headers = {}
        if app_id:
            headers["X-Tsign-Open-App-Id"] = app_id
        
        result = certificate_service.make_api_request(
            path="/cert/create",
            data=request_data,
            headers=headers,
            environment=environment,
            service="cert",
            operation="创建证书"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"创建证书异常: {str(e)}")
        return certificate_service.formatter.error(
            message=f"创建证书异常: {str(e)}",
            details={"cert_name": cert_name, "phone": phone}
        )


def query_certificate_detail(
    cert_id: str,
    app_id: Optional[str] = None,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    查询证书详情
    
    Args:
        cert_id: 证书ID
        app_id: 应用ID，不传则使用环境默认值
        environment: 环境描述，支持自然语言
        
    Returns:
        证书详情
    """
    try:
        # 构建请求数据
        request_data = {
            "certId": cert_id
        }
        
        # 如果指定了app_id，添加到请求头
        headers = {}
        if app_id:
            headers["X-Tsign-Open-App-Id"] = app_id
        
        result = certificate_service.make_api_request(
            path="/cert/query",
            data=request_data,
            headers=headers,
            environment=environment,
            service="cert",
            operation="查询证书详情"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"查询证书详情异常: {str(e)}")
        return certificate_service.formatter.error(
            message=f"查询证书详情异常: {str(e)}",
            details={"cert_id": cert_id}
        )


def revoke_certificate(
    cert_info_id: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    吊销数字证书
    
    Args:
        cert_info_id: 证书信息ID
        environment: 环境描述，支持自然语言
        
    Returns:
        吊销结果
    """
    try:
        # 构建请求数据
        request_data = {
            "certInfoId": cert_info_id
        }
        
        result = certificate_service.make_api_request(
            path="/cert/revoke",
            data=request_data,
            environment=environment,
            service="cert",
            operation="吊销证书"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"吊销证书异常: {str(e)}")
        return certificate_service.formatter.error(
            message=f"吊销证书异常: {str(e)}",
            details={"cert_info_id": cert_info_id}
        )


def update_certificate(
    cert_info_id: str,
    cert_name: str,
    mobile: str,
    license_number: str,
    algorithm: str = "SM2",
    cert_time: str = "ONEYEAR",
    config_id: Optional[str] = None,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    更新数字证书
    
    Args:
        cert_info_id: 证书信息ID
        cert_name: 证书姓名
        mobile: 手机号
        license_number: 证件号
        algorithm: 加密算法
        cert_time: 证书有效期
        config_id: 配置ID
        environment: 环境描述，支持自然语言
        
    Returns:
        更新结果
    """
    try:
        # 构建请求数据
        request_data = {
            "certInfoId": cert_info_id,
            "certName": cert_name,
            "mobile": mobile,
            "licenseNumber": license_number,
            "algorithm": algorithm,
            "certTime": cert_time
        }
        
        # 如果指定了config_id，添加到请求头
        headers = {}
        if config_id:
            headers["X-Tsign-Open-App-Id"] = config_id
        
        result = certificate_service.make_api_request(
            path="/cert/update",
            data=request_data,
            headers=headers,
            environment=environment,
            service="cert",
            operation="更新证书"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"更新证书异常: {str(e)}")
        return certificate_service.formatter.error(
            message=f"更新证书异常: {str(e)}",
            details={"cert_info_id": cert_info_id, "cert_name": cert_name}
        )


def get_user_info(
    user_id: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    获取用户信息
    
    Args:
        user_id: 用户ID
        environment: 环境描述，支持自然语言
        
    Returns:
        用户信息
    """
    try:
        # 构建请求数据
        request_data = {
            "userId": user_id
        }
        
        result = certificate_service.make_api_request(
            path="/user/info",
            data=request_data,
            environment=environment,
            service="sdk",
            operation="获取用户信息"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"获取用户信息异常: {str(e)}")
        return certificate_service.formatter.error(
            message=f"获取用户信息异常: {str(e)}",
            details={"user_id": user_id}
        )
